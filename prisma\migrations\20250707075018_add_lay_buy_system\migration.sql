/*
  Warnings:

  - The values [ARCHIVED] on the enum `MessageStatus` will be removed. If these variants are still used in the database, this will fail.

*/
-- CreateEnum
CREATE TYPE "LayBuyStatus" AS ENUM ('ACTIVE', 'COMPLETED', 'CANCELLED', 'FORFEITED', 'EXTENDED');

-- CreateEnum
CREATE TYPE "LayBuyPaymentStatus" AS ENUM ('PENDING', 'PAID', 'OVERDUE', 'FAILED');

-- AlterEnum
BEGIN;
CREATE TYPE "MessageStatus_new" AS ENUM ('UNREAD', 'READ', 'REPLIED', 'RESOLVED');
ALTER TABLE "contact_message" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "contact_message" ALTER COLUMN "status" TYPE "MessageStatus_new" USING ("status"::text::"MessageStatus_new");
ALTER TYPE "MessageStatus" RENAME TO "MessageStatus_old";
ALTER TYPE "MessageStatus_new" RENAME TO "MessageStatus";
DROP TYPE "MessageStatus_old";
ALTER TABLE "contact_message" ALTER COLUMN "status" SET DEFAULT 'UNREAD';
COMMIT;

-- CreateTable
CREATE TABLE "lay_buy_order" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "orderNumber" TEXT NOT NULL,
    "status" "LayBuyStatus" NOT NULL DEFAULT 'ACTIVE',
    "originalAmount" DOUBLE PRECISION NOT NULL,
    "layBuyAmount" DOUBLE PRECISION NOT NULL,
    "amountPaid" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "remainingAmount" DOUBLE PRECISION NOT NULL,
    "shippingAddress" TEXT NOT NULL,
    "phoneNumber" TEXT NOT NULL,
    "notes" TEXT,
    "adminNotes" TEXT,
    "startDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "dueDate" TIMESTAMP(3) NOT NULL,
    "gracePeriodEnd" TIMESTAMP(3) NOT NULL,
    "completedAt" TIMESTAMP(3),
    "cancelledAt" TIMESTAMP(3),
    "forfeitedAt" TIMESTAMP(3),
    "refundAmount" DOUBLE PRECISION,
    "termsAccepted" BOOLEAN NOT NULL DEFAULT false,
    "termsAcceptedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "lay_buy_order_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "lay_buy_item" (
    "id" TEXT NOT NULL,
    "layBuyOrderId" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "originalPrice" DOUBLE PRECISION NOT NULL,
    "layBuyPrice" DOUBLE PRECISION NOT NULL,
    "size" TEXT,
    "color" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "lay_buy_item_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "lay_buy_payment" (
    "id" TEXT NOT NULL,
    "layBuyOrderId" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "status" "LayBuyPaymentStatus" NOT NULL DEFAULT 'PENDING',
    "paymentMethod" TEXT,
    "paymentProof" TEXT,
    "notes" TEXT,
    "verifiedBy" TEXT,
    "verifiedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "lay_buy_payment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "lay_buy_reminder" (
    "id" TEXT NOT NULL,
    "layBuyOrderId" TEXT NOT NULL,
    "weekNumber" INTEGER NOT NULL,
    "sentAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "emailSent" BOOLEAN NOT NULL DEFAULT false,
    "smsSent" BOOLEAN NOT NULL DEFAULT false,
    "reminderType" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "lay_buy_reminder_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "notice_read" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "noticeId" TEXT NOT NULL,
    "readAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "notice_read_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "lay_buy_order_orderNumber_key" ON "lay_buy_order"("orderNumber");

-- CreateIndex
CREATE UNIQUE INDEX "notice_read_userId_noticeId_key" ON "notice_read"("userId", "noticeId");

-- AddForeignKey
ALTER TABLE "lay_buy_order" ADD CONSTRAINT "lay_buy_order_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lay_buy_item" ADD CONSTRAINT "lay_buy_item_layBuyOrderId_fkey" FOREIGN KEY ("layBuyOrderId") REFERENCES "lay_buy_order"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lay_buy_item" ADD CONSTRAINT "lay_buy_item_productId_fkey" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lay_buy_payment" ADD CONSTRAINT "lay_buy_payment_layBuyOrderId_fkey" FOREIGN KEY ("layBuyOrderId") REFERENCES "lay_buy_order"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "lay_buy_reminder" ADD CONSTRAINT "lay_buy_reminder_layBuyOrderId_fkey" FOREIGN KEY ("layBuyOrderId") REFERENCES "lay_buy_order"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notice_read" ADD CONSTRAINT "notice_read_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notice_read" ADD CONSTRAINT "notice_read_noticeId_fkey" FOREIGN KEY ("noticeId") REFERENCES "notice"("id") ON DELETE CASCADE ON UPDATE CASCADE;
