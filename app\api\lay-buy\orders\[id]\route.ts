import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-client";
import { prisma } from "@/lib/prisma";
import { ApiResponse } from "@/utils/types";
import { processCancellation } from "@/lib/lay-buy-forfeiture";

// GET /api/lay-buy/orders/[id] - Get specific lay-buy order
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const layBuyOrder = await prisma.layBuyOrder.findFirst({
      where: {
        id: params.id,
        userId: user.id, // Ensure user can only access their own orders
      },
      include: {
        layBuyItems: {
          include: {
            product: true,
          },
        },
        layBuyPayments: {
          orderBy: { createdAt: "desc" },
        },
        layBuyReminders: {
          orderBy: { createdAt: "desc" },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!layBuyOrder) {
      return NextResponse.json(
        { success: false, error: "Lay-Buy order not found" },
        { status: 404 }
      );
    }

    const response: ApiResponse = {
      success: true,
      data: layBuyOrder,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching lay-buy order:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch lay-buy order" },
      { status: 500 }
    );
  }
}

// PATCH /api/lay-buy/orders/[id] - Update lay-buy order (for cancellation, etc.)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, reason } = body;

    // Find the lay-buy order
    const layBuyOrder = await prisma.layBuyOrder.findFirst({
      where: {
        id: params.id,
        userId: user.id,
      },
      include: {
        layBuyItems: {
          include: {
            product: true,
          },
        },
      },
    });

    if (!layBuyOrder) {
      return NextResponse.json(
        { success: false, error: "Lay-Buy order not found" },
        { status: 404 }
      );
    }

    if (layBuyOrder.status !== "ACTIVE" && layBuyOrder.status !== "EXTENDED") {
      return NextResponse.json(
        { success: false, error: "Cannot modify this lay-buy order" },
        { status: 400 }
      );
    }

    let updatedOrder;

    if (action === "cancel") {
      // Use the comprehensive cancellation system
      const cancellationResult = await processCancellation(
        params.id,
        reason ? `Customer cancellation reason: ${reason}` : "Customer cancellation"
      );

      if (!cancellationResult.success) {
        return NextResponse.json(
          { success: false, error: cancellationResult.error || "Failed to cancel order" },
          { status: 400 }
        );
      }

      // Get the updated order
      updatedOrder = await prisma.layBuyOrder.findFirst({
        where: { id: params.id },
        include: {
          layBuyItems: {
            include: {
              product: true,
            },
          },
        },
      });

      if (!updatedOrder) {
        return NextResponse.json(
          { success: false, error: "Failed to retrieve updated order" },
          { status: 500 }
        );
      }
    } else {
      return NextResponse.json(
        { success: false, error: "Invalid action" },
        { status: 400 }
      );
    }

    const response: ApiResponse = {
      success: true,
      data: updatedOrder,
      message: action === "cancel" ? "Lay-Buy order cancelled successfully" : "Lay-Buy order updated successfully",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error updating lay-buy order:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update lay-buy order" },
      { status: 500 }
    );
  }
}
