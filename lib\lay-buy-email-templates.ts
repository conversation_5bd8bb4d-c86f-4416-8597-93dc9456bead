import { formatPrice } from "./product-utils";

export interface LayBuyEmailData {
  customerName: string;
  orderNumber: string;
  layBuyAmount: number;
  amountPaid: number;
  remainingAmount: number;
  dueDate: string;
  daysRemaining: number;
  weekNumber: number;
  orderItems: Array<{
    name: string;
    quantity: number;
    price: string;
    size?: string;
  }>;
  orderUrl: string;
  paymentUrl: string;
}

export const layBuyEmailTemplates = {
  // Week 1-4: Regular reminders
  weeklyReminder: (data: LayBuyEmailData) => ({
    subject: `Lay-Buy Reminder: Week ${data.weekNumber} - Order #${data.orderNumber}`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Lay-Buy Payment Reminder</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #3b82f6; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
          .progress-bar { background: #e5e7eb; height: 20px; border-radius: 10px; margin: 20px 0; }
          .progress-fill { background: #10b981; height: 100%; border-radius: 10px; transition: width 0.3s ease; }
          .order-summary { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .item { display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #e5e7eb; }
          .button { display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Lay-Buy Payment Reminder</h1>
            <p>Week ${data.weekNumber} of 6</p>
          </div>
          
          <div class="content">
            <h2>Hi ${data.customerName},</h2>
            
            <p>This is your weekly reminder for Lay-Buy order <strong>#${data.orderNumber}</strong>.</p>
            
            <div class="order-summary">
              <h3>Payment Progress</h3>
              <div class="progress-bar">
                <div class="progress-fill" style="width: ${(data.amountPaid / data.layBuyAmount) * 100}%"></div>
              </div>
              <div style="display: flex; justify-content: space-between; margin-top: 10px;">
                <span>Paid: ${formatPrice(data.amountPaid)}</span>
                <span>Remaining: ${formatPrice(data.remainingAmount)}</span>
              </div>
              
              <h3 style="margin-top: 30px;">Order Details</h3>
              ${data.orderItems.map(item => `
                <div class="item">
                  <span>${item.name} ${item.size ? `(${item.size})` : ''} x${item.quantity}</span>
                  <span>${item.price}</span>
                </div>
              `).join('')}
              
              <div style="margin-top: 20px; padding-top: 20px; border-top: 2px solid #e5e7eb;">
                <div style="display: flex; justify-content: space-between; font-weight: bold;">
                  <span>Total Lay-Buy Amount:</span>
                  <span>${formatPrice(data.layBuyAmount)}</span>
                </div>
              </div>
            </div>
            
            <div style="background: #dbeafe; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #1e40af; margin-top: 0;">⏰ Payment Due</h3>
              <p style="margin: 0; color: #1e40af;">
                <strong>${data.daysRemaining} days remaining</strong> until your payment is due on ${data.dueDate}.
              </p>
            </div>
            
            <div style="text-align: center;">
              <a href="${data.paymentUrl}" class="button">Make Payment Now</a>
            </div>
            
            <p>You can also view your complete order details and payment history by visiting your <a href="${data.orderUrl}">Lay-Buy dashboard</a>.</p>
            
            <p>Thank you for choosing our Lay-Buy service!</p>
          </div>
          
          <div class="footer">
            <p>This is an automated reminder. If you have any questions, please contact our support team.</p>
            <p>Rivv E-commerce | <a href="mailto:<EMAIL>"><EMAIL></a></p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
      Lay-Buy Payment Reminder - Week ${data.weekNumber}
      
      Hi ${data.customerName},
      
      This is your weekly reminder for Lay-Buy order #${data.orderNumber}.
      
      Payment Progress:
      - Paid: ${formatPrice(data.amountPaid)}
      - Remaining: ${formatPrice(data.remainingAmount)}
      - Due Date: ${data.dueDate}
      - Days Remaining: ${data.daysRemaining}
      
      Order Items:
      ${data.orderItems.map(item => `- ${item.name} ${item.size ? `(${item.size})` : ''} x${item.quantity} - ${item.price}`).join('\n')}
      
      Total Lay-Buy Amount: ${formatPrice(data.layBuyAmount)}
      
      Make a payment: ${data.paymentUrl}
      View order details: ${data.orderUrl}
      
      Thank you for choosing our Lay-Buy service!
    `
  }),

  // Week 5: Urgent reminder
  urgentReminder: (data: LayBuyEmailData) => ({
    subject: `🚨 URGENT: Lay-Buy Payment Due Soon - Order #${data.orderNumber}`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Urgent Lay-Buy Payment Reminder</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #f59e0b; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #fef3c7; padding: 30px; border-radius: 0 0 8px 8px; }
          .urgent-notice { background: #fecaca; border: 2px solid #ef4444; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .button { display: inline-block; background: #ef4444; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; margin: 20px 0; font-weight: bold; }
          .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚨 URGENT PAYMENT REMINDER</h1>
            <p>Week ${data.weekNumber} of 6 - Action Required</p>
          </div>
          
          <div class="content">
            <h2>Hi ${data.customerName},</h2>
            
            <div class="urgent-notice">
              <h3 style="color: #dc2626; margin-top: 0;">⚠️ Payment Due Very Soon!</h3>
              <p style="margin: 0; color: #dc2626; font-weight: bold;">
                Your Lay-Buy payment for order #${data.orderNumber} is due in only ${data.daysRemaining} days (${data.dueDate}).
              </p>
            </div>
            
            <p><strong>Outstanding Balance: ${formatPrice(data.remainingAmount)}</strong></p>
            
            <p>To avoid forfeiture of your order and payment, please complete your payment immediately.</p>
            
            <div style="text-align: center;">
              <a href="${data.paymentUrl}" class="button">PAY NOW - ${formatPrice(data.remainingAmount)}</a>
            </div>
            
            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3>What happens if I don't pay on time?</h3>
              <ul>
                <li>You'll have a 1-week grace period after the due date</li>
                <li>If payment isn't received during the grace period, your order will be forfeited</li>
                <li>Forfeited orders result in loss of all payments made (no refund)</li>
                <li>Items will be returned to inventory</li>
              </ul>
            </div>
            
            <p>If you're experiencing difficulties, please contact us immediately at <a href="mailto:<EMAIL>"><EMAIL></a> or call +266 6284 4473.</p>
          </div>
          
          <div class="footer">
            <p>This is an automated urgent reminder. Please take immediate action.</p>
            <p>Rivv E-commerce | <a href="mailto:<EMAIL>"><EMAIL></a></p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
      🚨 URGENT: Lay-Buy Payment Due Soon
      
      Hi ${data.customerName},
      
      Your Lay-Buy payment for order #${data.orderNumber} is due in only ${data.daysRemaining} days (${data.dueDate}).
      
      Outstanding Balance: ${formatPrice(data.remainingAmount)}
      
      To avoid forfeiture, please pay immediately: ${data.paymentUrl}
      
      What happens if you don't pay on time:
      - 1-week grace period after due date
      - Forfeiture if not paid during grace period
      - Loss of all payments made (no refund)
      
      Contact us if you need help: <EMAIL> or +266 6284 4473
    `
  }),

  // Week 6: Final warning
  finalWarning: (data: LayBuyEmailData) => ({
    subject: `🔴 FINAL WARNING: Lay-Buy Payment Overdue - Order #${data.orderNumber}`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Final Warning - Lay-Buy Payment Overdue</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #dc2626; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #fef2f2; padding: 30px; border-radius: 0 0 8px 8px; }
          .critical-notice { background: #fee2e2; border: 3px solid #dc2626; padding: 25px; border-radius: 8px; margin: 20px 0; }
          .button { display: inline-block; background: #dc2626; color: white; padding: 18px 36px; text-decoration: none; border-radius: 6px; margin: 20px 0; font-weight: bold; font-size: 18px; }
          .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔴 FINAL WARNING</h1>
            <p>Payment Overdue - Immediate Action Required</p>
          </div>
          
          <div class="content">
            <h2>Hi ${data.customerName},</h2>
            
            <div class="critical-notice">
              <h3 style="color: #dc2626; margin-top: 0;">🚨 PAYMENT OVERDUE - GRACE PERIOD ACTIVE</h3>
              <p style="margin: 0; color: #dc2626; font-weight: bold; font-size: 16px;">
                Your Lay-Buy payment for order #${data.orderNumber} was due on ${data.dueDate} and is now ${Math.abs(data.daysRemaining)} days overdue.
              </p>
              <p style="color: #dc2626; font-weight: bold; margin-top: 15px;">
                You have ${7 + data.daysRemaining} days left in your grace period to avoid forfeiture.
              </p>
            </div>
            
            <p style="font-size: 18px;"><strong>Outstanding Balance: ${formatPrice(data.remainingAmount)}</strong></p>
            
            <p><strong>This is your final warning.</strong> If payment is not received within the grace period, your order will be automatically forfeited and you will lose all payments made (${formatPrice(data.amountPaid)}).</p>
            
            <div style="text-align: center;">
              <a href="${data.paymentUrl}" class="button">URGENT: PAY NOW</a>
            </div>
            
            <div style="background: white; padding: 25px; border-radius: 8px; margin: 20px 0; border-left: 5px solid #dc2626;">
              <h3 style="color: #dc2626;">Immediate Action Required:</h3>
              <ol>
                <li><strong>Pay the outstanding balance immediately</strong> to secure your order</li>
                <li><strong>Contact us</strong> if you need assistance or have payment difficulties</li>
                <li><strong>Don't delay</strong> - forfeiture is automatic after the grace period</li>
              </ol>
            </div>
            
            <p style="background: #fee2e2; padding: 15px; border-radius: 8px; border: 1px solid #fca5a5;">
              <strong>Need Help?</strong> Contact us immediately:<br>
              📧 Email: <a href="mailto:<EMAIL>"><EMAIL></a><br>
              📞 Phone: +266 6284 4473
            </p>
          </div>
          
          <div class="footer">
            <p>This is an automated final warning. Immediate action is required to avoid forfeiture.</p>
            <p>Rivv E-commerce | <a href="mailto:<EMAIL>"><EMAIL></a></p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
      🔴 FINAL WARNING: Payment Overdue
      
      Hi ${data.customerName},
      
      Your Lay-Buy payment for order #${data.orderNumber} was due on ${data.dueDate} and is now ${Math.abs(data.daysRemaining)} days overdue.
      
      Grace period remaining: ${7 + data.daysRemaining} days
      Outstanding Balance: ${formatPrice(data.remainingAmount)}
      
      THIS IS YOUR FINAL WARNING.
      
      If payment is not received within the grace period, your order will be forfeited and you will lose all payments made (${formatPrice(data.amountPaid)}).
      
      PAY NOW: ${data.paymentUrl}
      
      Need help? Contact us immediately:
      Email: <EMAIL>
      Phone: +266 6284 4473
    `
  }),

  // Grace period reminder
  gracePeriodReminder: (data: LayBuyEmailData) => ({
    subject: `⏰ GRACE PERIOD: ${Math.abs(data.daysRemaining)} Days to Avoid Forfeiture - Order #${data.orderNumber}`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Grace Period - Last Chance</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #7c2d12; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #fef7ed; padding: 30px; border-radius: 0 0 8px 8px; }
          .countdown { background: #fed7aa; border: 3px solid #ea580c; padding: 30px; border-radius: 8px; margin: 20px 0; text-align: center; }
          .button { display: inline-block; background: #7c2d12; color: white; padding: 20px 40px; text-decoration: none; border-radius: 6px; margin: 20px 0; font-weight: bold; font-size: 20px; }
          .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>⏰ GRACE PERIOD ACTIVE</h1>
            <p>Last Chance to Save Your Order</p>
          </div>
          
          <div class="content">
            <h2>Hi ${data.customerName},</h2>
            
            <div class="countdown">
              <h2 style="color: #ea580c; margin: 0; font-size: 36px;">${Math.abs(data.daysRemaining)}</h2>
              <p style="color: #ea580c; margin: 10px 0 0 0; font-weight: bold; font-size: 18px;">
                DAYS LEFT TO AVOID FORFEITURE
              </p>
            </div>
            
            <p style="font-size: 18px; text-align: center;"><strong>Order #${data.orderNumber}</strong></p>
            <p style="font-size: 18px; text-align: center;"><strong>Outstanding: ${formatPrice(data.remainingAmount)}</strong></p>
            
            <p>Your Lay-Buy order is currently in the grace period. This is your <strong>last chance</strong> to complete payment and secure your order.</p>
            
            <div style="text-align: center;">
              <a href="${data.paymentUrl}" class="button">SAVE MY ORDER - PAY NOW</a>
            </div>
            
            <div style="background: #fee2e2; padding: 25px; border-radius: 8px; margin: 20px 0; border: 2px solid #fca5a5;">
              <h3 style="color: #dc2626; margin-top: 0;">⚠️ What Happens After the Grace Period:</h3>
              <ul style="color: #dc2626;">
                <li><strong>Your order will be automatically forfeited</strong></li>
                <li><strong>You will lose all payments made: ${formatPrice(data.amountPaid)}</strong></li>
                <li><strong>Items will be returned to inventory</strong></li>
                <li><strong>No refunds will be processed</strong></li>
              </ul>
            </div>
            
            <p style="text-align: center; font-size: 16px;">
              <strong>Don't lose your ${formatPrice(data.amountPaid)} investment!</strong><br>
              Complete your payment today to secure your order.
            </p>
            
            <p style="background: #fef3c7; padding: 15px; border-radius: 8px; border: 1px solid #fbbf24;">
              <strong>Emergency Contact:</strong><br>
              📧 <a href="mailto:<EMAIL>"><EMAIL></a><br>
              📞 +266 6284 4473<br>
              💬 WhatsApp: +266 6284 4473
            </p>
          </div>
          
          <div class="footer">
            <p>This is an automated grace period reminder. Time is running out!</p>
            <p>Rivv E-commerce | <a href="mailto:<EMAIL>"><EMAIL></a></p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
      ⏰ GRACE PERIOD: ${Math.abs(data.daysRemaining)} Days Left
      
      Hi ${data.customerName},
      
      LAST CHANCE to save your Lay-Buy order #${data.orderNumber}!
      
      Days remaining: ${Math.abs(data.daysRemaining)}
      Outstanding balance: ${formatPrice(data.remainingAmount)}
      
      After the grace period:
      - Order will be forfeited
      - You'll lose all payments: ${formatPrice(data.amountPaid)}
      - No refunds processed
      
      PAY NOW: ${data.paymentUrl}
      
      Emergency contact:
      Email: <EMAIL>
      Phone: +266 6284 4473
    `
  }),

  // Payment confirmation
  paymentConfirmation: (data: LayBuyEmailData & { paymentAmount: number; isCompleted: boolean }) => ({
    subject: data.isCompleted 
      ? `✅ Lay-Buy Completed! Order #${data.orderNumber} Ready for Delivery`
      : `✅ Payment Received - Order #${data.orderNumber}`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Payment Confirmation</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #10b981; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f0fdf4; padding: 30px; border-radius: 0 0 8px 8px; }
          .success-notice { background: #dcfce7; border: 2px solid #10b981; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .button { display: inline-block; background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>✅ Payment Confirmed!</h1>
            <p>${data.isCompleted ? 'Lay-Buy Completed' : 'Payment Received'}</p>
          </div>
          
          <div class="content">
            <h2>Hi ${data.customerName},</h2>
            
            <div class="success-notice">
              <h3 style="color: #059669; margin-top: 0;">
                ${data.isCompleted ? '🎉 Congratulations! Your Lay-Buy is Complete!' : '💰 Payment Successfully Received'}
              </h3>
              <p style="margin: 0; color: #059669;">
                We've received your payment of ${formatPrice(data.paymentAmount)} for order #${data.orderNumber}.
              </p>
            </div>
            
            ${data.isCompleted ? `
              <p><strong>Great news!</strong> You've completed all payments for your Lay-Buy order. Your items are now ready for processing and delivery.</p>
              
              <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>What happens next?</h3>
                <ol>
                  <li>Your order will be processed within 1-2 business days</li>
                  <li>You'll receive a shipping confirmation with tracking details</li>
                  <li>Standard delivery times apply based on your location</li>
                </ol>
              </div>
            ` : `
              <p>Thank you for your payment! Here's your updated payment status:</p>
              
              <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>Updated Payment Status</h3>
                <p><strong>Total Paid:</strong> ${formatPrice(data.amountPaid)}</p>
                <p><strong>Remaining Balance:</strong> ${formatPrice(data.remainingAmount)}</p>
                <p><strong>Payment Due Date:</strong> ${data.dueDate}</p>
                <p><strong>Days Remaining:</strong> ${data.daysRemaining}</p>
              </div>
            `}
            
            <div style="text-align: center;">
              <a href="${data.orderUrl}" class="button">View Order Details</a>
            </div>
            
            <p>Thank you for choosing our Lay-Buy service!</p>
          </div>
          
          <div class="footer">
            <p>This is an automated confirmation. Keep this email for your records.</p>
            <p>Rivv E-commerce | <a href="mailto:<EMAIL>"><EMAIL></a></p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
      ✅ Payment Confirmed!
      
      Hi ${data.customerName},
      
      ${data.isCompleted ? 'Congratulations! Your Lay-Buy is complete!' : 'Payment successfully received!'}
      
      Payment amount: ${formatPrice(data.paymentAmount)}
      Order: #${data.orderNumber}
      
      ${data.isCompleted ? `
      Your order is now ready for processing and delivery.
      
      What's next:
      1. Order processing (1-2 business days)
      2. Shipping confirmation with tracking
      3. Standard delivery times apply
      ` : `
      Updated status:
      - Total paid: ${formatPrice(data.amountPaid)}
      - Remaining: ${formatPrice(data.remainingAmount)}
      - Due date: ${data.dueDate}
      - Days remaining: ${data.daysRemaining}
      `}
      
      View order: ${data.orderUrl}
      
      Thank you for choosing our Lay-Buy service!
    `
  })
};
