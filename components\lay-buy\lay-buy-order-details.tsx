"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  CreditCard, 
  Clock, 
  DollarSign,
  AlertTriangle,
  CheckCircle,
  ArrowLeft,
  Calendar,
  Package,
  User as UserIcon,
  Phone,
  MapPin,
  FileText,
  Download,
  Eye,
  XCircle,
  RefreshCw
} from "lucide-react";
import { formatPrice } from "@/lib/product-utils";
import { LayBuyOrder, LayBuyPayment, User } from "@/utils/types";
import { calculateDaysRemaining } from "@/actions/layBuyActions";
import Link from "next/link";
import Image from "next/image";

interface LayBuyOrderDetailsProps {
  user: User;
  layBuyOrderId: string;
}

export default function LayBuyOrderDetails({ user, layBuyOrderId }: LayBuyOrderDetailsProps) {
  const [layBuyOrder, setLayBuyOrder] = useState<LayBuyOrder | null>(null);
  const [payments, setPayments] = useState<LayBuyPayment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchLayBuyOrder();
    fetchPayments();
  }, [layBuyOrderId]);

  const fetchLayBuyOrder = async () => {
    try {
      const response = await fetch(`/api/lay-buy/orders/${layBuyOrderId}`);
      const result = await response.json();
      
      if (result.success) {
        setLayBuyOrder(result.data);
      } else {
        setError(result.error || "Failed to fetch Lay-Buy order");
      }
    } catch (err) {
      setError("Failed to load Lay-Buy order");
    }
  };

  const fetchPayments = async () => {
    try {
      const response = await fetch(`/api/lay-buy/payments?layBuyOrderId=${layBuyOrderId}`);
      const result = await response.json();
      
      if (result.success) {
        setPayments(result.data || []);
      }
    } catch (err) {
      console.error("Failed to load payments:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelOrder = async () => {
    if (!confirm("Are you sure you want to cancel this Lay-Buy order? This action cannot be undone.")) {
      return;
    }

    try {
      const response = await fetch(`/api/lay-buy/orders/${layBuyOrderId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "cancel",
          reason: "Customer cancellation",
        }),
      });

      const result = await response.json();
      if (result.success) {
        await fetchLayBuyOrder();
      } else {
        setError(result.error || "Failed to cancel order");
      }
    } catch (err) {
      setError("Failed to cancel order");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!layBuyOrder) {
    return (
      <div className="text-center py-8">
        <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold mb-2">Order Not Found</h2>
        <p className="text-gray-600 mb-4">The Lay-Buy order you're looking for doesn't exist or you don't have access to it.</p>
        <Link href="/lay-buy">
          <Button>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Lay-Buy Orders
          </Button>
        </Link>
      </div>
    );
  }

  const timing = calculateDaysRemaining(new Date(layBuyOrder.dueDate), new Date(layBuyOrder.gracePeriodEnd));
  const paymentProgress = (layBuyOrder.amountPaid / layBuyOrder.layBuyAmount) * 100;
  const canMakePayment = layBuyOrder.status === "ACTIVE" && layBuyOrder.remainingAmount > 0;
  const canCancel = layBuyOrder.status === "ACTIVE" || layBuyOrder.status === "EXTENDED";

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ACTIVE": return "bg-blue-100 text-blue-800";
      case "COMPLETED": return "bg-green-100 text-green-800";
      case "CANCELLED": return "bg-gray-100 text-gray-800";
      case "FORFEITED": return "bg-red-100 text-red-800";
      case "EXTENDED": return "bg-yellow-100 text-yellow-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "ACTIVE": return <Clock className="h-4 w-4" />;
      case "COMPLETED": return <CheckCircle className="h-4 w-4" />;
      case "CANCELLED": return <XCircle className="h-4 w-4" />;
      case "FORFEITED": return <AlertTriangle className="h-4 w-4" />;
      case "EXTENDED": return <Calendar className="h-4 w-4" />;
      default: return <Package className="h-4 w-4" />;
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "PAID": return "bg-green-100 text-green-800";
      case "PENDING": return "bg-yellow-100 text-yellow-800";
      case "FAILED": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Lay-Buy Order Details</h1>
          <p className="text-gray-600">Order #{layBuyOrder.orderNumber}</p>
        </div>
        <div className="flex gap-2">
          <Link href="/lay-buy">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Orders
            </Button>
          </Link>
          {canMakePayment && (
            <Link href={`/lay-buy/${layBuyOrderId}/payment`}>
              <Button>
                <DollarSign className="h-4 w-4 mr-2" />
                Make Payment
              </Button>
            </Link>
          )}
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}

      {/* Order Status and Progress */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Order Status
              </CardTitle>
              <Badge className={getStatusColor(layBuyOrder.status)}>
                {getStatusIcon(layBuyOrder.status)}
                <span className="ml-1">{layBuyOrder.status}</span>
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Payment Progress */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Payment Progress</span>
                <span>{formatPrice(layBuyOrder.amountPaid)} / {formatPrice(layBuyOrder.layBuyAmount)}</span>
              </div>
              <Progress value={paymentProgress} className="h-3" />
              <div className="flex justify-between text-xs text-gray-600">
                <span>{paymentProgress.toFixed(1)}% completed</span>
                <span>{formatPrice(layBuyOrder.remainingAmount)} remaining</span>
              </div>
            </div>

            <Separator />

            {/* Financial Summary */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Original Total:</span>
                <span className="ml-2 font-medium">{formatPrice(layBuyOrder.originalAmount)}</span>
              </div>
              <div>
                <span className="text-gray-600">Lay-Buy Amount:</span>
                <span className="ml-2 font-medium">{formatPrice(layBuyOrder.layBuyAmount)}</span>
              </div>
              <div>
                <span className="text-gray-600">Amount Paid:</span>
                <span className="ml-2 font-medium text-green-600">{formatPrice(layBuyOrder.amountPaid)}</span>
              </div>
              <div>
                <span className="text-gray-600">Remaining:</span>
                <span className="ml-2 font-medium text-orange-600">{formatPrice(layBuyOrder.remainingAmount)}</span>
              </div>
            </div>

            {/* Due Date Info */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 text-blue-700 mb-2">
                <Calendar className="h-4 w-4" />
                <span className="font-medium">Payment Schedule</span>
              </div>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Due Date:</span>
                  <span className="font-medium">{new Date(layBuyOrder.dueDate).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Grace Period Ends:</span>
                  <span className="font-medium">{new Date(layBuyOrder.gracePeriodEnd).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Status:</span>
                  <span className={`font-medium ${
                    timing.daysUntilDue > 0 ? 'text-blue-600' : 
                    timing.isInGracePeriod ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {timing.daysUntilDue > 0 ? (
                      `${timing.daysUntilDue} days remaining`
                    ) : timing.isInGracePeriod ? (
                      `Grace period: ${Math.abs(timing.daysUntilForfeiture)} days left`
                    ) : (
                      `Overdue by ${Math.abs(timing.daysUntilDue)} days`
                    )}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {canMakePayment && (
              <Link href={`/lay-buy/${layBuyOrderId}/payment`}>
                <Button className="w-full">
                  <DollarSign className="h-4 w-4 mr-2" />
                  Make Payment
                </Button>
              </Link>
            )}
            
            <Button variant="outline" className="w-full" onClick={() => window.print()}>
              <Download className="h-4 w-4 mr-2" />
              Print Order
            </Button>

            {canCancel && (
              <Button variant="destructive" className="w-full" onClick={handleCancelOrder}>
                <XCircle className="h-4 w-4 mr-2" />
                Cancel Order
              </Button>
            )}

            <Button variant="outline" className="w-full" onClick={() => {
              fetchLayBuyOrder();
              fetchPayments();
            }}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Information Tabs */}
      <Tabs defaultValue="items" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="items">Order Items</TabsTrigger>
          <TabsTrigger value="payments">Payment History</TabsTrigger>
          <TabsTrigger value="delivery">Delivery Info</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
        </TabsList>

        {/* Order Items Tab */}
        <TabsContent value="items">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Order Items ({layBuyOrder.layBuyItems?.length || 0})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {layBuyOrder.layBuyItems?.map((item, index) => (
                  <div key={index} className="flex items-start gap-4 p-4 border rounded-lg">
                    {item.product.images && item.product.images.length > 0 && (
                      <div className="w-16 h-16 relative flex-shrink-0">
                        <Image
                          src={item.product.images[0]}
                          alt={item.product.name}
                          fill
                          className="object-cover rounded-md"
                        />
                      </div>
                    )}
                    <div className="flex-1">
                      <h4 className="font-medium">{item.product.name}</h4>
                      <p className="text-sm text-gray-600">{item.product.description}</p>
                      {(item.size || item.color) && (
                        <div className="flex gap-4 mt-1 text-sm text-gray-600">
                          {item.size && <span>Size: {item.size}</span>}
                          {item.color && <span>Color: {item.color}</span>}
                        </div>
                      )}
                      <div className="flex gap-4 mt-2 text-sm">
                        <span>Quantity: {item.quantity}</span>
                        <span>Unit Price: {formatPrice(item.layBuyPrice)}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatPrice(item.layBuyPrice * item.quantity)}</div>
                      <div className="text-sm text-gray-500 line-through">
                        {formatPrice(item.originalPrice * item.quantity)}
                      </div>
                      <div className="text-xs text-green-600">
                        Save {formatPrice((item.originalPrice - item.layBuyPrice) * item.quantity)}
                      </div>
                    </div>
                  </div>
                ))}

                <Separator />

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Subtotal (Original):</span>
                    <span className="line-through text-gray-500">{formatPrice(layBuyOrder.originalAmount)}</span>
                  </div>
                  <div className="flex justify-between font-medium">
                    <span>Lay-Buy Total (60%):</span>
                    <span>{formatPrice(layBuyOrder.layBuyAmount)}</span>
                  </div>
                  <div className="flex justify-between text-green-600">
                    <span>Total Savings:</span>
                    <span>{formatPrice(layBuyOrder.originalAmount - layBuyOrder.layBuyAmount)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Payment History Tab */}
        <TabsContent value="payments">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Payment History ({payments.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {payments.length === 0 ? (
                <div className="text-center py-8">
                  <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Payments Yet</h3>
                  <p className="text-gray-600">Payment history will appear here once payments are made.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {payments.map((payment) => (
                    <div key={payment.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium">{formatPrice(payment.amount)}</span>
                          <Badge className={getPaymentStatusColor(payment.status)}>
                            {payment.status}
                          </Badge>
                        </div>
                        <div className="text-sm text-gray-600">
                          <div>Method: {payment.paymentMethod || "Not specified"}</div>
                          <div>Date: {new Date(payment.createdAt).toLocaleDateString()}</div>
                          {payment.notes && <div>Notes: {payment.notes}</div>}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        {payment.paymentProof && (
                          <Button variant="outline" size="sm" asChild>
                            <a href={payment.paymentProof} target="_blank" rel="noopener noreferrer">
                              <Eye className="h-4 w-4 mr-2" />
                              View Proof
                            </a>
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Delivery Info Tab */}
        <TabsContent value="delivery">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Delivery Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-3">Customer Details</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <UserIcon className="h-4 w-4 text-gray-500" />
                      <span>{user.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-gray-500" />
                      <span>{layBuyOrder.phoneNumber}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-3">Delivery Address</h4>
                  <div className="flex items-start gap-2 text-sm">
                    <MapPin className="h-4 w-4 text-gray-500 mt-0.5" />
                    <span>{layBuyOrder.shippingAddress}</span>
                  </div>
                </div>
              </div>

              {layBuyOrder.notes && (
                <div>
                  <h4 className="font-medium mb-2">Special Instructions</h4>
                  <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                    {layBuyOrder.notes}
                  </p>
                </div>
              )}

              <div className="bg-yellow-50 p-4 rounded-lg">
                <h4 className="font-medium text-yellow-800 mb-2">Delivery Notice</h4>
                <p className="text-sm text-yellow-700">
                  {layBuyOrder.status === "COMPLETED"
                    ? "Your order will be processed for delivery within 1-2 business days."
                    : "Delivery will commence once your Lay-Buy payment is completed."
                  }
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Timeline Tab */}
        <TabsContent value="timeline">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Order Timeline
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <div>
                    <div className="font-medium">Order Created</div>
                    <div className="text-sm text-gray-600">
                      {new Date(layBuyOrder.createdAt).toLocaleDateString()} at {new Date(layBuyOrder.createdAt).toLocaleTimeString()}
                    </div>
                    <div className="text-sm text-gray-500">Lay-Buy order initiated with 60% payment</div>
                  </div>
                </div>

                {layBuyOrder.termsAcceptedAt && (
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                    <div>
                      <div className="font-medium">Terms Accepted</div>
                      <div className="text-sm text-gray-600">
                        {new Date(layBuyOrder.termsAcceptedAt).toLocaleDateString()}
                      </div>
                      <div className="text-sm text-gray-500">Customer agreed to Lay-Buy terms and conditions</div>
                    </div>
                  </div>
                )}

                {payments.map((payment) => (
                  <div key={payment.id} className="flex items-start gap-3">
                    <div className={`w-2 h-2 rounded-full mt-2 ${
                      payment.status === "PAID" ? "bg-green-600" :
                      payment.status === "PENDING" ? "bg-yellow-600" : "bg-red-600"
                    }`}></div>
                    <div>
                      <div className="font-medium">Payment {payment.status === "PAID" ? "Received" : "Submitted"}</div>
                      <div className="text-sm text-gray-600">
                        {new Date(payment.createdAt).toLocaleDateString()}
                      </div>
                      <div className="text-sm text-gray-500">
                        {formatPrice(payment.amount)} via {payment.paymentMethod || "unspecified method"}
                      </div>
                    </div>
                  </div>
                ))}

                {layBuyOrder.completedAt && (
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                    <div>
                      <div className="font-medium">Lay-Buy Completed</div>
                      <div className="text-sm text-gray-600">
                        {new Date(layBuyOrder.completedAt).toLocaleDateString()}
                      </div>
                      <div className="text-sm text-gray-500">All payments received, order ready for delivery</div>
                    </div>
                  </div>
                )}

                {layBuyOrder.cancelledAt && (
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-gray-600 rounded-full mt-2"></div>
                    <div>
                      <div className="font-medium">Order Cancelled</div>
                      <div className="text-sm text-gray-600">
                        {new Date(layBuyOrder.cancelledAt).toLocaleDateString()}
                      </div>
                      <div className="text-sm text-gray-500">
                        Refund amount: {layBuyOrder.refundAmount ? formatPrice(layBuyOrder.refundAmount) : "Processing"}
                      </div>
                    </div>
                  </div>
                )}

                {layBuyOrder.forfeitedAt && (
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-red-600 rounded-full mt-2"></div>
                    <div>
                      <div className="font-medium">Order Forfeited</div>
                      <div className="text-sm text-gray-600">
                        {new Date(layBuyOrder.forfeitedAt).toLocaleDateString()}
                      </div>
                      <div className="text-sm text-gray-500">Payment deadline exceeded, order forfeited</div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
