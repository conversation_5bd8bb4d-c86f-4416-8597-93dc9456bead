// Import Prisma types
import {
  User as PrismaUser,
  Product as PrismaProduct,
  Category as PrismaCategory,
  Order as PrismaOrder,
  OrderItem as PrismaOrderItem,
  CartItem as PrismaCartItem,
  Review as PrismaReview,
  DiscountCode as PrismaDiscountCode,
  PaymentProof as PrismaPaymentProof,
  Notice as PrismaNotice,
  Testimonial as PrismaTestimonial,
  ContactMessage as PrismaContactMessage,
  LayBuyOrder as PrismaLayBuyOrder,
  LayBuyItem as PrismaLayBuyItem,
  LayBuyPayment as PrismaLayBuyPayment,
  LayBuyReminder as PrismaLayBuyReminder,
  UserRole,
  OrderStatus,
  PaymentStatus,
  DiscountType,
  NoticeType,
  MessageStatus,
  LayBuyStatus,
  LayBuyPaymentStatus
} from "@prisma/client";

export type User = {
  id: string;
  name: string;
  emailVerified: boolean;
  email: string;
  role: UserRole;
  createdAt: Date;
  updatedAt: Date;
  image?: string | null | undefined;
};

// Legacy type for backward compatibility
export type ShoeType = {
  id: string;
  name: string;
  image: string;
  discountedPrice: number;
  price: number;
  discount: boolean;
  rating: number;
  numberOfRatings: number;
};

// Enhanced Product types
export type Product = PrismaProduct & {
  category: PrismaCategory;
  reviews?: PrismaReview[];
  _count?: {
    reviews: number;
    orderItems: number;
  };
  isFeatured?: boolean;
};

export type ProductWithDetails = Product & {
  orderItems?: PrismaOrderItem[];
  cartItems?: PrismaCartItem[];
};

// Cart types
export type CartItem = PrismaCartItem & {
  product: Product;
};

export type CartSummary = {
  items: CartItem[];
  totalItems: number;
  totalPrice: number;
};

// Order types
export type Order = PrismaOrder & {
  user?: User | null;
  orderItems: (PrismaOrderItem & { product: Product })[];
  discountCode?: PrismaDiscountCode | null;
  paymentProof?: PrismaPaymentProof | null;
};

export type OrderSummary = {
  subtotal: number;
  discountAmount: number;
  total: number;
  itemCount: number;
};

// Filter types for product listing
export type ProductFilters = {
  search?: string;
  categoryId?: string;
  brand?: string;
  minPrice?: number;
  maxPrice?: number;
  sizes?: string[];
  colors?: string[];
  sortBy?: 'name' | 'price' | 'rating' | 'newest';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
};

// Lay-Buy types
export type LayBuyOrder = PrismaLayBuyOrder & {
  user?: User | null;
  layBuyItems: (PrismaLayBuyItem & { product: Product })[];
  layBuyPayments?: PrismaLayBuyPayment[];
  layBuyReminders?: PrismaLayBuyReminder[];
};

export type LayBuyItem = PrismaLayBuyItem & {
  product: Product;
};

export type LayBuyPayment = PrismaLayBuyPayment;

export type LayBuyReminder = PrismaLayBuyReminder;

export type LayBuyOrderSummary = {
  totalOrders: number;
  activeOrders: number;
  completedOrders: number;
  forfeitedOrders: number;
  totalValue: number;
  totalPaid: number;
  totalOutstanding: number;
};

// API Response types
export type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
};

export type PaginatedResponse<T> = {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
};

// Content Management types
export type Notice = PrismaNotice;
export type Testimonial = PrismaTestimonial;
export type ContactMessage = PrismaContactMessage;

// Discount and Payment types
export type DiscountCode = PrismaDiscountCode;
export type PaymentProof = PrismaPaymentProof;

// Export Prisma enums for use in components
export {
  UserRole,
  OrderStatus,
  PaymentStatus,
  DiscountType,
  NoticeType,
  MessageStatus
};


export enum ContactMessageStatus {
  UNREAD = "UNREAD",
  READ = "READ",
  REPLIED = "REPLIED",
  RESOLVED = "RESOLVED"
}