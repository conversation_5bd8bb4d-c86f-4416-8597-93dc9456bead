"use client";

import { useState, useEffect } from "react";
import { ContactMessage, ContactMessageStatus } from "@/utils/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  ArrowLeft,
  Mail,
  User,
  Calendar,
  MessageSquare,
  Save,
  CheckCircle,
  Eye,
  Clock
} from "lucide-react";
import Link from "next/link";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";

interface ContactMessageDetailProps {
  messageId: string;
}

export default function ContactMessageDetail({ messageId }: ContactMessageDetailProps) {
  const [message, setMessage] = useState<ContactMessage | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState(false);
  const [updateSuccess, setUpdateSuccess] = useState(false);
  const [adminNotes, setAdminNotes] = useState("");
  const [status, setStatus] = useState<ContactMessageStatus>("UNREAD" as ContactMessageStatus);
  const [replyMessage, setReplyMessage] = useState("");
  const [sendingReply, setSendingReply] = useState(false);
  const [replySuccess, setReplySuccess] = useState(false);

  useEffect(() => {
    const fetchMessage = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/contact/${messageId}`);
        const result = await response.json();

        if (result.success) {
          setMessage(result.data);
          setStatus(result.data.status);
          setAdminNotes(result.data.adminNotes || "");
          
          // Mark as read if it's unread
          if (result.data.status === "UNREAD") {
            updateMessageStatus("READ" as ContactMessageStatus);
          }
        } else {
          setError(result.error || "Message not found");
        }
      } catch (err) {
        setError("Failed to fetch message details");
        console.error("Error fetching message:", err);
      } finally {
        setLoading(false);
      }
    };

    if (messageId) {
      fetchMessage();
    }
  }, [messageId]);

  const updateMessageStatus = async (newStatus: ContactMessageStatus, notes?: string) => {
    try {
      const response = await fetch(`/api/contact/${messageId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: newStatus,
          adminNotes: notes !== undefined ? notes : adminNotes,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setMessage(result.data);
        setStatus(result.data.status);
        setAdminNotes(result.data.adminNotes || "");
        return true;
      } else {
        throw new Error(result.error || "Failed to update message");
      }
    } catch (error) {
      console.error("Error updating message:", error);
      return false;
    }
  };

  const handleSave = async () => {
    setUpdating(true);
    setUpdateSuccess(false);

    const success = await updateMessageStatus(status, adminNotes);
    
    if (success) {
      setUpdateSuccess(true);
      setTimeout(() => setUpdateSuccess(false), 3000);
    }
    
    setUpdating(false);
  };

  const handleSendReply = async () => {
    if (!replyMessage.trim()) {
      return;
    }

    setSendingReply(true);
    setReplySuccess(false);

    try {
      const response = await fetch(`/api/admin/contact-messages/${messageId}/reply`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          replyMessage: replyMessage.trim(),
        }),
      });

      const result = await response.json();

      if (result.success) {
        setReplySuccess(true);
        setReplyMessage("");
        setMessage(result.data);
        setStatus(result.data.status);
        setAdminNotes(result.data.adminNotes || "");
        setTimeout(() => setReplySuccess(false), 3000);
      } else {
        throw new Error(result.error || "Failed to send reply");
      }
    } catch (error) {
      console.error("Error sending reply:", error);
      alert("Failed to send reply email. Please try again.");
    } finally {
      setSendingReply(false);
    }
  };

  const getStatusIcon = (status: ContactMessageStatus) => {
    switch (status) {
      case "UNREAD":
        return <Mail className="h-5 w-5" />;
      case "READ":
        return <Eye className="h-5 w-5" />;
      case "REPLIED":
        return <MessageSquare className="h-5 w-5" />;
      case "RESOLVED":
        return <CheckCircle className="h-5 w-5" />;
      default:
        return <Clock className="h-5 w-5" />;
    }
  };

  const getStatusColor = (status: ContactMessageStatus) => {
    switch (status) {
      case "UNREAD":
        return "bg-red-100 text-red-800";
      case "READ":
        return "bg-blue-100 text-blue-800";
      case "REPLIED":
        return "bg-purple-100 text-purple-800";
      case "RESOLVED":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getSubjectLabel = (subject: string) => {
    const subjectMap: Record<string, string> = {
      general: "General Inquiry",
      order: "Order Support",
      product: "Product Question",
      shipping: "Shipping & Delivery",
      returns: "Returns & Refunds",
      technical: "Technical Support",
      partnership: "Business Partnership",
      feedback: "Feedback & Suggestions",
    };
    return subjectMap[subject] || subject;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <SpinnerCircle4 />
      </div>
    );
  }

  if (error || !message) {
    return (
      <div className="max-w-2xl mx-auto text-center py-12">
        <Mail className="mx-auto h-16 w-16 text-gray-400 mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Message not found</h2>
        <p className="text-gray-600 mb-6">{error}</p>
        <Link href="/admin/contact-messages">
          <Button>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Messages
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Link href="/admin/contact-messages">
            <Button variant="outline" size="sm" className="mb-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Messages
            </Button>
          </Link>
          <h1 className="text-3xl font-bold text-gray-900">
            {getSubjectLabel(message.subject)}
          </h1>
          <p className="text-gray-600">
            Message from {message.name}
          </p>
        </div>
        
        <Badge className={`${getStatusColor(message.status as ContactMessageStatus)} text-lg px-4 py-2`}>
          <div className="flex items-center gap-2">
            {getStatusIcon(message.status as ContactMessageStatus)}
            {message.status}
          </div>
        </Badge>
      </div>

      {/* Success Messages */}
      {updateSuccess && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            Message updated successfully!
          </AlertDescription>
        </Alert>
      )}

      {replySuccess && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            Reply email sent successfully!
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Message Content */}
          <Card>
            <CardHeader>
              <CardTitle>Message Content</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-gray-900 whitespace-pre-wrap">
                    {message.message}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Admin Notes */}
          <Card>
            <CardHeader>
              <CardTitle>Admin Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Textarea
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                  placeholder="Add internal notes about this message..."
                  rows={4}
                />
                <p className="text-sm text-gray-500">
                  These notes are for internal use only and will not be visible to the customer.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Reply Section */}
          <Card>
            <CardHeader>
              <CardTitle>Send Reply Email</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Textarea
                  value={replyMessage}
                  onChange={(e) => setReplyMessage(e.target.value)}
                  placeholder="Enter your reply message to the customer..."
                  rows={6}
                />
                <div className="flex justify-end">
                  <Button 
                    onClick={handleSendReply} 
                    disabled={sendingReply || !replyMessage.trim()}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {sendingReply ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Sending...
                      </>
                    ) : (
                      <>
                        <Mail className="mr-2 h-4 w-4" />
                        Send Reply
                      </>
                    )}
                  </Button>
                </div>
                <p className="text-sm text-gray-500">
                  This will send an email to {message.email} and update the message status to "Replied".
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="font-medium">{message.name}</p>
                  {message.userId && (
                    <p className="text-sm text-blue-600">Registered User</p>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Mail className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="font-medium">{message.email}</p>
                  <a 
                    href={`mailto:${message.email}`}
                    className="text-sm text-blue-600 hover:underline"
                  >
                    Send Email
                  </a>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="font-medium">Received</p>
                  <p className="text-sm text-gray-600">
                    {new Date(message.createdAt).toLocaleString()}
                  </p>
                </div>
              </div>
              
              {message.readAt && (
                <div className="flex items-center gap-3">
                  <Eye className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="font-medium">Read</p>
                    <p className="text-sm text-gray-600">
                      {new Date(message.readAt).toLocaleString()}
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Status Management */}
          <Card>
            <CardHeader>
              <CardTitle>Status Management</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Message Status
                </label>
                <Select value={status} onValueChange={(value: ContactMessageStatus) => setStatus(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="UNREAD">Unread</SelectItem>
                    <SelectItem value="READ">Read</SelectItem>
                    <SelectItem value="REPLIED">Replied</SelectItem>
                    <SelectItem value="RESOLVED">Resolved</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <Button 
                onClick={handleSave} 
                disabled={updating}
                className="w-full"
              >
                {updating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <a 
                href={`mailto:${message.email}?subject=Re: ${getSubjectLabel(message.subject)}`}
                className="w-full"
              >
                <Button variant="outline" className="w-full">
                  <Mail className="mr-2 h-4 w-4" />
                  Open Email Client
                </Button>
              </a>
              

            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
