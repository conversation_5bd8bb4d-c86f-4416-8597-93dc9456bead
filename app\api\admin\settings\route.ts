import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

// GET: Fetch global settings
export async function GET() {
  let settings = await prisma.settings.findFirst();
  if (!settings) {
    // Create default settings if not present
    settings = await prisma.settings.create({
      data: { 
        primaryColor: "#3b82f6",

      },
    });
  }
  return NextResponse.json({ success: true, data: settings });
}

// POST: Update global settings
export async function POST(req: NextRequest) {
  const body = await req.json();
  const { primaryColor } = body;
  
  let settings = await prisma.settings.findFirst();
  if (!settings) {
    settings = await prisma.settings.create({
      data: { 
        primaryColor: primaryColor || "#3b82f6",
      },
    });
  } else {
    settings = await prisma.settings.update({
      where: { id: settings.id },
      data: { 
        primaryColor: primaryColor !== undefined ? primaryColor : settings.primaryColor,
      },
    });
  }
  return NextResponse.json({ success: true, data: settings });
} 