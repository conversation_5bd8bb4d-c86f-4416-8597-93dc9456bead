"use client";

import { useState } from "react";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { AlertTriangle, Clock, CreditCard, RefreshCw, Shield } from "lucide-react";

interface LayBuyTermsProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: () => void;
  totalAmount: number;
  layBuyAmount: number;
}

export default function LayBuyTerms({ 
  isOpen, 
  onClose, 
  onAccept, 
  totalAmount, 
  layBuyAmount 
}: LayBuyTermsProps) {
  const [hasRead, setHasRead] = useState(false);
  const [hasAgreed, setHasAgreed] = useState(false);

  const handleAccept = () => {
    if (hasRead && hasAgreed) {
      onAccept();
    }
  };

  const handleClose = () => {
    setHasRead(false);
    setHasAgreed(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <CreditCard className="h-6 w-6 text-blue-600" />
            Lay-Buy Terms & Conditions
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="h-[60vh] pr-4">
          <div className="space-y-6">
            {/* Summary Section */}
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h3 className="font-semibold text-blue-800 mb-2">Your Lay-Buy Summary</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Original Total:</span>
                  <span className="font-semibold ml-2">M {totalAmount.toFixed(2)}</span>
                </div>
                <div>
                  <span className="text-gray-600">Lay-Buy Amount (60%):</span>
                  <span className="font-semibold ml-2 text-blue-600">M {layBuyAmount.toFixed(2)}</span>
                </div>
                <div>
                  <span className="text-gray-600">Initial Payment:</span>
                  <span className="font-semibold ml-2">M {layBuyAmount.toFixed(2)}</span>
                </div>
                <div>
                  <span className="text-gray-600">Payment Period:</span>
                  <span className="font-semibold ml-2">6 weeks + 1 week grace</span>
                </div>
              </div>
            </div>

            {/* Terms Content */}
            <div className="space-y-4">
              <section>
                <h3 className="font-semibold text-lg flex items-center gap-2 mb-3">
                  <Clock className="h-5 w-5 text-orange-600" />
                  1. Payment Terms
                </h3>
                <div className="space-y-2 text-sm text-gray-700 pl-7">
                  <p>• You agree to pay 60% of the total order value upfront to secure your Lay-Buy order.</p>
                  <p>• The remaining 40% must be paid within 6 weeks from the order date.</p>
                  <p>• A 1-week grace period is provided after the 6-week deadline.</p>
                  <p>• Weekly reminder emails will be sent to help you track your payment schedule.</p>
                  <p>• Payments can be made via M-Pesa, bank transfer, or other approved methods.</p>
                </div>
              </section>

              <Separator />

              <section>
                <h3 className="font-semibold text-lg flex items-center gap-2 mb-3">
                  <RefreshCw className="h-5 w-5 text-green-600" />
                  2. Cancellation & Refund Policy
                </h3>
                <div className="space-y-2 text-sm text-gray-700 pl-7">
                  <p>• <strong>Early Cancellation:</strong> If you cancel within the 6-week period, you will receive 50% of your paid amount as a refund.</p>
                  <p>• <strong>Late Cancellation:</strong> If you cancel after the 6-week period + grace period, no refund will be provided.</p>
                  <p>• Refunds will be processed within 7-14 business days via your original payment method.</p>
                  <p>• Cancellation requests must be submitted in writing via email or through your customer account.</p>
                </div>
              </section>

              <Separator />

              <section>
                <h3 className="font-semibold text-lg flex items-center gap-2 mb-3">
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                  3. Forfeiture Policy
                </h3>
                <div className="space-y-2 text-sm text-gray-700 pl-7">
                  <p>• If full payment is not received within 6 weeks + 1 week grace period, your Lay-Buy will be marked as "Forfeited".</p>
                  <p>• Forfeited Lay-Buys result in the loss of your entire paid amount (no refund).</p>
                  <p>• Products from forfeited Lay-Buys will be returned to general inventory.</p>
                  <p>• You will receive escalating reminder emails during weeks 5, 6, and the grace period.</p>
                </div>
              </section>

              <Separator />

              <section>
                <h3 className="font-semibold text-lg flex items-center gap-2 mb-3">
                  <Shield className="h-5 w-5 text-purple-600" />
                  4. Product Reservation & Delivery
                </h3>
                <div className="space-y-2 text-sm text-gray-700 pl-7">
                  <p>• Products are reserved for you once the initial 60% payment is confirmed.</p>
                  <p>• Items will be held in our warehouse until full payment is received.</p>
                  <p>• Delivery will commence only after 100% payment completion.</p>
                  <p>• Standard delivery terms and fees apply once payment is complete.</p>
                  <p>• Product availability is guaranteed during the Lay-Buy period.</p>
                </div>
              </section>

              <Separator />

              <section>
                <h3 className="font-semibold text-lg mb-3">5. Additional Terms</h3>
                <div className="space-y-2 text-sm text-gray-700 pl-7">
                  <p>• Lay-Buy is available for orders with a minimum value of M 700.00.</p>
                  <p>• You may have a maximum of 3 active Lay-Buy orders at any time.</p>
                  <p>• Price changes or promotions after Lay-Buy creation do not affect your locked-in price.</p>
                  <p>• Extensions may be granted at management discretion for exceptional circumstances.</p>
                  <p>• These terms are subject to change with 30 days notice via email.</p>
                  <p>• By proceeding, you acknowledge that you have read, understood, and agree to these terms.</p>
                </div>
              </section>

              <Separator />

              <section className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <h3 className="font-semibold text-yellow-800 mb-2">Important Reminders</h3>
                <div className="space-y-1 text-sm text-yellow-700">
                  <p>• Keep track of your payment deadlines to avoid forfeiture</p>
                  <p>• Contact us immediately if you anticipate payment difficulties</p>
                  <p>• Check your email regularly for payment reminders</p>
                  <p>• You can view your Lay-Buy status anytime in your account dashboard</p>
                </div>
              </section>
            </div>
          </div>
        </ScrollArea>

        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="hasRead" 
              checked={hasRead} 
              onCheckedChange={setHasRead}
            />
            <label htmlFor="hasRead" className="text-sm font-medium">
              I have read and understood the complete Lay-Buy Terms & Conditions
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox 
              id="hasAgreed" 
              checked={hasAgreed} 
              onCheckedChange={setHasAgreed}
              disabled={!hasRead}
            />
            <label htmlFor="hasAgreed" className="text-sm font-medium">
              I agree to the Lay-Buy Terms & Conditions and understand the payment schedule and policies
            </label>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleAccept}
            disabled={!hasRead || !hasAgreed}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Accept Terms & Continue with Lay-Buy
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
