import { prisma } from "@/lib/prisma";

export interface LayBuyAnalytics {
  overview: {
    totalOrders: number;
    totalValue: number;
    totalPaid: number;
    totalOutstanding: number;
    averageOrderValue: number;
    completionRate: number;
    forfeitureRate: number;
  };
  statusBreakdown: {
    active: number;
    completed: number;
    cancelled: number;
    forfeited: number;
    extended: number;
  };
  financialMetrics: {
    totalRevenue: number;
    totalForfeited: number;
    totalRefunded: number;
    averagePaymentTime: number; // days
    collectionEfficiency: number; // percentage
  };
  timeSeriesData: {
    date: string;
    ordersCreated: number;
    ordersCompleted: number;
    ordersForfeited: number;
    revenue: number;
  }[];
  riskMetrics: {
    overdueOrders: number;
    gracePeriodOrders: number;
    highRiskCustomers: number;
    averageDaysToComplete: number;
  };
  customerInsights: {
    totalCustomers: number;
    repeatCustomers: number;
    averageOrdersPerCustomer: number;
    topCustomers: Array<{
      customerId: string;
      customerName: string;
      totalOrders: number;
      totalValue: number;
      completionRate: number;
    }>;
  };
}

export interface LayBuyReport {
  generatedAt: Date;
  period: {
    start: Date;
    end: Date;
    description: string;
  };
  analytics: LayBuyAnalytics;
  recommendations: string[];
  alerts: Array<{
    type: "warning" | "error" | "info";
    message: string;
    count?: number;
  }>;
}

/**
 * Generate comprehensive Lay-Buy analytics
 */
export async function generateLayBuyAnalytics(
  startDate?: Date,
  endDate?: Date
): Promise<LayBuyAnalytics> {
  const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
  const end = endDate || new Date();

  // Base query conditions
  const dateFilter = {
    createdAt: {
      gte: start,
      lte: end,
    },
  };

  // Get all orders in the period
  const allOrders = await prisma.layBuyOrder.findMany({
    where: dateFilter,
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      layBuyPayments: true,
    },
  });

  // Calculate overview metrics
  const totalOrders = allOrders.length;
  const totalValue = allOrders.reduce((sum, order) => sum + order.originalAmount, 0);
  const totalPaid = allOrders.reduce((sum, order) => sum + order.amountPaid, 0);
  const totalOutstanding = allOrders.reduce((sum, order) => sum + order.remainingAmount, 0);
  const averageOrderValue = totalOrders > 0 ? totalValue / totalOrders : 0;

  const completedOrders = allOrders.filter(order => order.status === "COMPLETED").length;
  const forfeitedOrders = allOrders.filter(order => order.status === "FORFEITED").length;
  const completionRate = totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0;
  const forfeitureRate = totalOrders > 0 ? (forfeitedOrders / totalOrders) * 100 : 0;

  // Status breakdown
  const statusBreakdown = {
    active: allOrders.filter(order => order.status === "ACTIVE").length,
    completed: completedOrders,
    cancelled: allOrders.filter(order => order.status === "CANCELLED").length,
    forfeited: forfeitedOrders,
    extended: allOrders.filter(order => order.status === "EXTENDED").length,
  };

  // Financial metrics
  const totalRevenue = allOrders
    .filter(order => order.status === "COMPLETED")
    .reduce((sum, order) => sum + order.layBuyAmount, 0);
  
  const totalForfeited = allOrders
    .filter(order => order.status === "FORFEITED")
    .reduce((sum, order) => sum + order.amountPaid, 0);
  
  const totalRefunded = allOrders
    .filter(order => order.status === "CANCELLED" && order.refundAmount)
    .reduce((sum, order) => sum + (order.refundAmount || 0), 0);

  // Calculate average payment time
  const completedOrdersWithTime = allOrders.filter(order => 
    order.status === "COMPLETED" && order.completedAt
  );
  const averagePaymentTime = completedOrdersWithTime.length > 0
    ? completedOrdersWithTime.reduce((sum, order) => {
        const days = Math.floor(
          (new Date(order.completedAt!).getTime() - new Date(order.createdAt).getTime()) 
          / (1000 * 60 * 60 * 24)
        );
        return sum + days;
      }, 0) / completedOrdersWithTime.length
    : 0;

  const collectionEfficiency = totalValue > 0 ? (totalPaid / totalValue) * 100 : 0;

  // Time series data (daily breakdown)
  const timeSeriesData = await generateTimeSeriesData(start, end);

  // Risk metrics
  const now = new Date();
  const overdueOrders = allOrders.filter(order => 
    order.status === "ACTIVE" && new Date(order.dueDate) < now
  ).length;
  
  const gracePeriodOrders = allOrders.filter(order => 
    order.status === "ACTIVE" && 
    new Date(order.dueDate) < now && 
    new Date(order.gracePeriodEnd) > now
  ).length;

  // Customer insights
  const customerInsights = await generateCustomerInsights(start, end);

  return {
    overview: {
      totalOrders,
      totalValue,
      totalPaid,
      totalOutstanding,
      averageOrderValue,
      completionRate,
      forfeitureRate,
    },
    statusBreakdown,
    financialMetrics: {
      totalRevenue,
      totalForfeited,
      totalRefunded,
      averagePaymentTime,
      collectionEfficiency,
    },
    timeSeriesData,
    riskMetrics: {
      overdueOrders,
      gracePeriodOrders,
      highRiskCustomers: 0, // TODO: Implement risk scoring
      averageDaysToComplete: averagePaymentTime,
    },
    customerInsights,
  };
}

/**
 * Generate time series data for charts
 */
async function generateTimeSeriesData(start: Date, end: Date) {
  const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
  const timeSeriesData = [];

  for (let i = 0; i < days; i++) {
    const date = new Date(start);
    date.setDate(date.getDate() + i);
    const nextDate = new Date(date);
    nextDate.setDate(nextDate.getDate() + 1);

    const dayOrders = await prisma.layBuyOrder.findMany({
      where: {
        createdAt: {
          gte: date,
          lt: nextDate,
        },
      },
    });

    const completedOrders = await prisma.layBuyOrder.findMany({
      where: {
        completedAt: {
          gte: date,
          lt: nextDate,
        },
      },
    });

    const forfeitedOrders = await prisma.layBuyOrder.findMany({
      where: {
        forfeitedAt: {
          gte: date,
          lt: nextDate,
        },
      },
    });

    timeSeriesData.push({
      date: date.toISOString().split('T')[0],
      ordersCreated: dayOrders.length,
      ordersCompleted: completedOrders.length,
      ordersForfeited: forfeitedOrders.length,
      revenue: completedOrders.reduce((sum, order) => sum + order.layBuyAmount, 0),
    });
  }

  return timeSeriesData;
}

/**
 * Generate customer insights
 */
async function generateCustomerInsights(start: Date, end: Date) {
  const customerOrders = await prisma.layBuyOrder.groupBy({
    by: ['userId'],
    where: {
      createdAt: {
        gte: start,
        lte: end,
      },
    },
    _count: {
      id: true,
    },
    _sum: {
      originalAmount: true,
    },
  });

  const totalCustomers = customerOrders.length;
  const repeatCustomers = customerOrders.filter(customer => customer._count.id > 1).length;
  const averageOrdersPerCustomer = totalCustomers > 0 
    ? customerOrders.reduce((sum, customer) => sum + customer._count.id, 0) / totalCustomers 
    : 0;

  // Get top customers
  const topCustomersData = await Promise.all(
    customerOrders
      .sort((a, b) => (b._sum.originalAmount || 0) - (a._sum.originalAmount || 0))
      .slice(0, 10)
      .map(async (customer) => {
        const user = await prisma.user.findUnique({
          where: { id: customer.userId },
          select: { id: true, name: true },
        });

        const userOrders = await prisma.layBuyOrder.findMany({
          where: { userId: customer.userId },
        });

        const completedCount = userOrders.filter(order => order.status === "COMPLETED").length;
        const completionRate = userOrders.length > 0 ? (completedCount / userOrders.length) * 100 : 0;

        return {
          customerId: customer.userId,
          customerName: user?.name || "Unknown",
          totalOrders: customer._count.id,
          totalValue: customer._sum.originalAmount || 0,
          completionRate,
        };
      })
  );

  return {
    totalCustomers,
    repeatCustomers,
    averageOrdersPerCustomer,
    topCustomers: topCustomersData,
  };
}

/**
 * Generate comprehensive Lay-Buy report
 */
export async function generateLayBuyReport(
  startDate?: Date,
  endDate?: Date
): Promise<LayBuyReport> {
  const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  const end = endDate || new Date();

  const analytics = await generateLayBuyAnalytics(start, end);

  // Generate recommendations based on analytics
  const recommendations: string[] = [];
  const alerts: LayBuyReport['alerts'] = [];

  // Analyze completion rate
  if (analytics.overview.completionRate < 70) {
    recommendations.push("Consider implementing additional payment reminders or incentives to improve completion rate");
    alerts.push({
      type: "warning",
      message: "Low completion rate detected",
      count: Math.round(analytics.overview.completionRate),
    });
  }

  // Analyze forfeiture rate
  if (analytics.overview.forfeitureRate > 20) {
    recommendations.push("High forfeiture rate indicates need for better customer screening or payment terms adjustment");
    alerts.push({
      type: "error",
      message: "High forfeiture rate",
      count: Math.round(analytics.overview.forfeitureRate),
    });
  }

  // Analyze overdue orders
  if (analytics.riskMetrics.overdueOrders > 0) {
    recommendations.push("Follow up on overdue orders to prevent forfeiture");
    alerts.push({
      type: "warning",
      message: "Orders currently overdue",
      count: analytics.riskMetrics.overdueOrders,
    });
  }

  // Analyze collection efficiency
  if (analytics.financialMetrics.collectionEfficiency < 80) {
    recommendations.push("Improve collection processes to increase payment efficiency");
  }

  // Analyze customer retention
  const retentionRate = analytics.customerInsights.totalCustomers > 0 
    ? (analytics.customerInsights.repeatCustomers / analytics.customerInsights.totalCustomers) * 100 
    : 0;
  
  if (retentionRate < 30) {
    recommendations.push("Focus on customer retention strategies to increase repeat Lay-Buy usage");
  }

  return {
    generatedAt: new Date(),
    period: {
      start,
      end,
      description: `${start.toLocaleDateString()} to ${end.toLocaleDateString()}`,
    },
    analytics,
    recommendations,
    alerts,
  };
}

/**
 * Export Lay-Buy data to CSV format
 */
export async function exportLayBuyData(
  startDate?: Date,
  endDate?: Date,
  format: "csv" | "json" = "csv"
): Promise<string> {
  const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  const end = endDate || new Date();

  const orders = await prisma.layBuyOrder.findMany({
    where: {
      createdAt: {
        gte: start,
        lte: end,
      },
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      layBuyItems: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              price: true,
            },
          },
        },
      },
      layBuyPayments: true,
    },
    orderBy: {
      createdAt: "desc",
    },
  });

  if (format === "json") {
    return JSON.stringify(orders, null, 2);
  }

  // Generate CSV
  const headers = [
    "Order Number",
    "Customer Name",
    "Customer Email",
    "Status",
    "Original Amount",
    "Lay-Buy Amount",
    "Amount Paid",
    "Remaining Amount",
    "Created Date",
    "Due Date",
    "Completed Date",
    "Items Count",
    "Payment Count",
  ];

  const rows = orders.map(order => [
    order.orderNumber,
    order.user?.name || "",
    order.user?.email || "",
    order.status,
    order.originalAmount.toFixed(2),
    order.layBuyAmount.toFixed(2),
    order.amountPaid.toFixed(2),
    order.remainingAmount.toFixed(2),
    order.createdAt.toISOString().split('T')[0],
    order.dueDate.toISOString().split('T')[0],
    order.completedAt ? order.completedAt.toISOString().split('T')[0] : "",
    order.layBuyItems?.length || 0,
    order.layBuyPayments?.length || 0,
  ]);

  const csvContent = [headers, ...rows]
    .map(row => row.map(cell => `"${cell}"`).join(","))
    .join("\n");

  return csvContent;
}
