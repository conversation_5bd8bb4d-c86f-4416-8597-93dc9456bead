"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CreditCard, Clock, Shield, Info, CheckCircle } from "lucide-react";
import { formatPrice } from "@/lib/product-utils";
import LayBuyTerms from "./lay-buy-terms";

interface LayBuyOptionProps {
  totalAmount: number;
  onLayBuySelect: (isLayBuy: boolean, layBuyAmount?: number) => void;
  selectedPaymentType: "full" | "laybuy";
  disabled?: boolean;
}

export default function LayBuyOption({ 
  totalAmount, 
  onLayBuySelect, 
  selectedPaymentType,
  disabled = false 
}: LayBuyOptionProps) {
  const [showTerms, setShowTerms] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);

  const layBuyAmount = totalAmount * 0.6; // 60% of total
  const remainingAmount = totalAmount * 0.4; // 40% remaining
  const isEligible = totalAmount >= 700; // Minimum M700 for Lay-Buy

  const handlePaymentTypeChange = (value: string) => {
    if (value === "laybuy") {
      if (!termsAccepted) {
        setShowTerms(true);
      } else {
        onLayBuySelect(true, layBuyAmount);
      }
    } else {
      onLayBuySelect(false);
    }
  };

  const handleTermsAccept = () => {
    setTermsAccepted(true);
    setShowTerms(false);
    onLayBuySelect(true, layBuyAmount);
  };

  const handleTermsClose = () => {
    setShowTerms(false);
    // If terms were not accepted, revert to full payment
    if (!termsAccepted) {
      onLayBuySelect(false);
    }
  };

  if (!isEligible) {
    return (
      <Card className="border-gray-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <CreditCard className="h-5 w-5" />
            Payment Options
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              Lay-Buy is available for orders of M 700.00 and above. Your current order total is {formatPrice(totalAmount)}.
            </AlertDescription>
          </Alert>
          
          <div className="mt-4">
            <div className="flex items-center space-x-2 p-4 border rounded-lg bg-white">
              <RadioGroupItem value="full" id="full" checked={true} />
              <Label htmlFor="full" className="flex items-center gap-3 cursor-pointer flex-1">
                <CreditCard className="h-5 w-5 text-green-600" />
                <div>
                  <div className="font-medium">Full Payment</div>
                  <div className="text-sm text-gray-500">
                    Pay {formatPrice(totalAmount)} now
                  </div>
                </div>
              </Label>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="border-gray-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <CreditCard className="h-5 w-5" />
            Payment Options
          </CardTitle>
        </CardHeader>
        <CardContent>
          <RadioGroup 
            value={selectedPaymentType} 
            onValueChange={handlePaymentTypeChange}
            disabled={disabled}
          >
            {/* Full Payment Option */}
            <div className="flex items-center space-x-2 p-4 border rounded-lg">
              <RadioGroupItem value="full" id="full" />
              <Label htmlFor="full" className="flex items-center gap-3 cursor-pointer flex-1">
                <CreditCard className="h-5 w-5 text-green-600" />
                <div>
                  <div className="font-medium">Full Payment</div>
                  <div className="text-sm text-gray-500">
                    Pay {formatPrice(totalAmount)} now and receive your order immediately
                  </div>
                </div>
              </Label>
            </div>

            {/* Lay-Buy Option */}
            <div className="flex items-center space-x-2 p-4 border rounded-lg bg-blue-50 border-blue-200">
              <RadioGroupItem value="laybuy" id="laybuy" />
              <Label htmlFor="laybuy" className="flex items-center gap-3 cursor-pointer flex-1">
                <Clock className="h-5 w-5 text-blue-600" />
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium">Lay-Buy Option</span>
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                      Save 40%
                    </Badge>
                    {termsAccepted && (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    )}
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>Pay only {formatPrice(layBuyAmount)} now (60% of total)</div>
                    <div>Complete payment within 6 weeks</div>
                  </div>
                </div>
              </Label>
            </div>
          </RadioGroup>

          {/* Lay-Buy Details */}
          {selectedPaymentType === "laybuy" && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <h4 className="font-semibold text-blue-800 mb-3 flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Your Lay-Buy Breakdown
              </h4>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Original Total:</span>
                  <span className="line-through text-gray-500">{formatPrice(totalAmount)}</span>
                </div>
                <div className="flex justify-between font-semibold text-blue-600">
                  <span>Pay Today (60%):</span>
                  <span>{formatPrice(layBuyAmount)}</span>
                </div>
                <div className="flex justify-between text-gray-600">
                  <span>Remaining (40%):</span>
                  <span>{formatPrice(remainingAmount)}</span>
                </div>
                
                <Separator className="my-3" />
                
                <div className="space-y-1 text-xs text-blue-700">
                  <div className="flex items-center gap-2">
                    <Clock className="h-3 w-3" />
                    <span>6 weeks to complete payment + 1 week grace period</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Shield className="h-3 w-3" />
                    <span>Items reserved until payment completion</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Info className="h-3 w-3" />
                    <span>Weekly email reminders to track progress</span>
                  </div>
                </div>

                {termsAccepted && (
                  <div className="mt-3 p-2 bg-green-50 border border-green-200 rounded text-xs text-green-700">
                    ✓ Terms & Conditions accepted
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Important Notice */}
          {selectedPaymentType === "laybuy" && (
            <Alert className="mt-4">
              <Info className="h-4 w-4" />
              <AlertDescription className="text-xs">
                <strong>Important:</strong> By selecting Lay-Buy, you agree to our payment schedule. 
                Early cancellation results in 50% refund, late cancellation results in forfeiture of all payments.
                {!termsAccepted && (
                  <Button 
                    variant="link" 
                    className="p-0 h-auto text-xs underline ml-1"
                    onClick={() => setShowTerms(true)}
                  >
                    Read full terms & conditions
                  </Button>
                )}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Terms & Conditions Modal */}
      <LayBuyTerms
        isOpen={showTerms}
        onClose={handleTermsClose}
        onAccept={handleTermsAccept}
        totalAmount={totalAmount}
        layBuyAmount={layBuyAmount}
      />
    </>
  );
}
