// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id
  name          String
  email         String
  emailVerified Boolean
  image         String?
  role          UserRole  @default(USER)
  createdAt     DateTime
  updatedAt     DateTime
  sessions      Session[]
  accounts      Account[]
  orders        Order[]
  cartItems     CartItem[]
  contactMessages ContactMessage[]
  noticesRead   NoticeRead[]

  @@unique([email])
  @@map("user")
}

enum UserRole {
  USER
  ADMIN
}

model Session {
  id        String   @id
  expiresAt DateTime
  token     String
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@map("session")
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

// Product and Category Models
model Category {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?
  image       String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]

  @@map("category")
}

model Product {
  id          String      @id @default(cuid())
  name        String
  description String?
  price       Float
  discountedPrice Float?
  brand       String
  category    Category    @relation(fields: [categoryId], references: [id])
  categoryId  String
  images      String[]    // Array of image URLs
  sizes       String[]    // Available sizes
  colors      String[]    // Available colors
  stock       Int         @default(0)
  isActive    Boolean     @default(true)
  rating      Float       @default(0)
  reviewCount Int         @default(0)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  orderItems  OrderItem[]
  cartItems   CartItem[]
  reviews     Review[]

  @@map("product")
}

model Review {
  id        String   @id @default(cuid())
  rating    Int      // 1-5 stars
  comment   String?
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  productId String
  userId    String
  userName  String   // Store user name for display
  userEmail String   // Store user email
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("review")
}

// Cart Models
model CartItem {
  id        String   @id @default(cuid())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  productId String
  quantity  Int      @default(1)
  size      String?
  color     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, productId, size, color])
  @@map("cart_item")
}

// Order Models
model Order {
  id              String      @id @default(cuid())
  user            User        @relation(fields: [userId], references: [id])
  userId          String
  orderNumber     String      @unique
  status          OrderStatus @default(PENDING)
  totalAmount     Float
  discountAmount  Float       @default(0)
  discountCode    DiscountCode? @relation(fields: [discountCodeId], references: [id])
  discountCodeId  String?
  shippingAddress String
  phoneNumber     String
  notes           String?
  adminNotes      String?     // Admin internal notes
  paymentProof    PaymentProof?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
  orderItems      OrderItem[]

  @@map("order")
}

model OrderItem {
  id        String   @id @default(cuid())
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  orderId   String
  product   Product  @relation(fields: [productId], references: [id])
  productId String
  quantity  Int
  price     Float    // Price at time of order
  size      String?
  color     String?
  createdAt DateTime @default(now())

  @@map("order_item")
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
}

// Discount Code Model
model DiscountCode {
  id          String            @id @default(cuid())
  code        String            @unique
  description String?
  type        DiscountType
  value       Float             // Percentage or fixed amount
  minAmount   Float?            // Minimum order amount
  maxUses     Int?              // Maximum number of uses
  usedCount   Int               @default(0)
  isActive    Boolean           @default(true)
  validFrom   DateTime          @default(now())
  validUntil  DateTime?
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  orders      Order[]

  @@map("discount_code")
}

enum DiscountType {
  PERCENTAGE
  FIXED_AMOUNT
}

// Payment Proof Model
model PaymentProof {
  id          String        @id @default(cuid())
  order       Order         @relation(fields: [orderId], references: [id], onDelete: Cascade)
  orderId     String        @unique
  imageUrl    String        // UploadThing URL
  status      PaymentStatus @default(PENDING)
  notes       String?       // Admin notes
  verifiedBy  String?       // Admin who verified
  verifiedAt  DateTime?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  @@map("payment_proof")
}

enum PaymentStatus {
  PENDING
  VERIFIED
  REJECTED
}

// Content Management Models
model Notice {
  id        String   @id @default(cuid())
  title     String
  content   String
  type      NoticeType @default(INFO)
  isActive  Boolean  @default(true)
  priority  Int      @default(0) // Higher number = higher priority
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  noticesRead NoticeRead[]

  @@map("notice")
}

enum NoticeType {
  INFO
  WARNING
  SUCCESS
  ERROR
}

model Testimonial {
  id        String   @id @default(cuid())
  name      String
  content   String
  rating    Int      // 1-5 stars
  image     String?  // Customer photo
  position  String?  // Job title or description
  isActive  Boolean  @default(true)
  priority  Int      @default(0) // Higher number = higher priority
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("testimonial")
}

// Contact Message Model
model ContactMessage {
  id        String        @id @default(cuid())
  user      User?         @relation(fields: [userId], references: [id])
  userId    String?
  name      String
  email     String
  subject   String
  message   String
  status    MessageStatus @default(UNREAD)
  adminNotes String?
  readAt    DateTime?
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  @@map("contact_message")
}

enum MessageStatus {
  UNREAD
  READ
  REPLIED
  RESOLVED
}

model Settings {
  id        String   @id @default(cuid())
  primaryColor String @default("#3b82f6")
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model NoticeRead {
  id        String   @id @default(cuid())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String
  notice    Notice   @relation(fields: [noticeId], references: [id], onDelete: Cascade)
  noticeId  String
  readAt    DateTime @default(now())

  @@unique([userId, noticeId])
  @@map("notice_read")
}
