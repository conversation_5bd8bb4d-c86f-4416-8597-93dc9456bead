"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { 
  CreditCard, 
  Clock, 
  DollarSign,
  AlertTriangle,
  CheckCircle,
  ArrowLeft,
  Upload,
  Calendar,
  Package,
  User as UserIcon,
  Phone,
  MapPin
} from "lucide-react";
import { formatPrice } from "@/lib/product-utils";
import { LayBuyOrder, User } from "@/utils/types";
import { calculateDaysRemaining } from "@/actions/layBuyActions";
import Link from "next/link";
import { useRouter } from "next/navigation";

interface LayBuyPaymentContentProps {
  user: User;
  layBuyOrderId: string;
}

export default function LayBuyPaymentContent({ user, layBuyOrderId }: LayBuyPaymentContentProps) {
  const [layBuyOrder, setLayBuyOrder] = useState<LayBuyOrder | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const router = useRouter();

  // Payment form state
  const [paymentAmount, setPaymentAmount] = useState("");
  const [paymentMethod, setPaymentMethod] = useState("");
  const [paymentProof, setPaymentProof] = useState<File | null>(null);
  const [notes, setNotes] = useState("");

  useEffect(() => {
    fetchLayBuyOrder();
  }, [layBuyOrderId]);

  const fetchLayBuyOrder = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/lay-buy/orders/${layBuyOrderId}`);
      const result = await response.json();
      
      if (result.success) {
        setLayBuyOrder(result.data);
        // Set default payment amount to remaining amount
        setPaymentAmount(result.data.remainingAmount.toString());
      } else {
        setError(result.error || "Failed to fetch Lay-Buy order");
      }
    } catch (err) {
      setError("Failed to load Lay-Buy order");
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type and size
      const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
      const maxSize = 5 * 1024 * 1024; // 5MB

      if (!allowedTypes.includes(file.type)) {
        setError("Please upload a valid image (JPEG, PNG) or PDF file");
        return;
      }

      if (file.size > maxSize) {
        setError("File size must be less than 5MB");
        return;
      }

      setPaymentProof(file);
      setError(null);
    }
  };

  const handleSubmitPayment = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!layBuyOrder) return;

    // Validation
    const amount = parseFloat(paymentAmount);
    if (isNaN(amount) || amount <= 0) {
      setError("Please enter a valid payment amount");
      return;
    }

    if (amount > layBuyOrder.remainingAmount) {
      setError(`Payment amount cannot exceed remaining balance of ${formatPrice(layBuyOrder.remainingAmount)}`);
      return;
    }

    if (!paymentMethod) {
      setError("Please select a payment method");
      return;
    }

    try {
      setSubmitting(true);
      setError(null);

      // Upload payment proof if provided
      let paymentProofUrl = "";
      if (paymentProof) {
        const formData = new FormData();
        formData.append("file", paymentProof);
        formData.append("type", "payment-proof");

        const uploadResponse = await fetch("/api/upload", {
          method: "POST",
          body: formData,
        });

        const uploadResult = await uploadResponse.json();
        if (uploadResult.success) {
          paymentProofUrl = uploadResult.data.url;
        } else {
          throw new Error("Failed to upload payment proof");
        }
      }

      // Submit payment
      const response = await fetch("/api/lay-buy/payments", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          layBuyOrderId,
          amount,
          paymentMethod,
          paymentProof: paymentProofUrl,
          notes,
        }),
      });

      const result = await response.json();
      if (result.success) {
        setSuccess(result.message || "Payment submitted successfully!");
        
        // Refresh order data
        await fetchLayBuyOrder();
        
        // Reset form
        setPaymentAmount("");
        setPaymentMethod("");
        setPaymentProof(null);
        setNotes("");

        // Redirect to order details after a delay
        setTimeout(() => {
          router.push(`/lay-buy/${layBuyOrderId}`);
        }, 3000);
      } else {
        setError(result.error || "Failed to submit payment");
      }
    } catch (err) {
      setError("Failed to submit payment. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!layBuyOrder) {
    return (
      <div className="text-center py-8">
        <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold mb-2">Order Not Found</h2>
        <p className="text-gray-600 mb-4">The Lay-Buy order you're looking for doesn't exist or you don't have access to it.</p>
        <Link href="/lay-buy">
          <Button>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Lay-Buy Orders
          </Button>
        </Link>
      </div>
    );
  }

  const timing = calculateDaysRemaining(new Date(layBuyOrder.dueDate), new Date(layBuyOrder.gracePeriodEnd));
  const paymentProgress = (layBuyOrder.amountPaid / layBuyOrder.layBuyAmount) * 100;
  const isCompleted = layBuyOrder.status === "COMPLETED";
  const canMakePayment = layBuyOrder.status === "ACTIVE" && layBuyOrder.remainingAmount > 0;

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Make Payment</h1>
          <p className="text-gray-600">Order #{layBuyOrder.orderNumber}</p>
        </div>
        <Link href={`/lay-buy/${layBuyOrderId}`}>
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Order
          </Button>
        </Link>
      </div>

      {/* Success Message */}
      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">{success}</AlertDescription>
        </Alert>
      )}

      {/* Error Message */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Payment Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!canMakePayment ? (
              <div className="text-center py-8">
                {isCompleted ? (
                  <>
                    <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-green-700 mb-2">Payment Complete!</h3>
                    <p className="text-gray-600">This Lay-Buy order has been fully paid.</p>
                  </>
                ) : (
                  <>
                    <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-yellow-700 mb-2">Payment Not Available</h3>
                    <p className="text-gray-600">This order cannot accept payments in its current status.</p>
                  </>
                )}
              </div>
            ) : (
              <form onSubmit={handleSubmitPayment} className="space-y-4">
                <div>
                  <Label htmlFor="amount">Payment Amount</Label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">M</span>
                    <Input
                      id="amount"
                      type="number"
                      step="0.01"
                      min="0.01"
                      max={layBuyOrder.remainingAmount}
                      value={paymentAmount}
                      onChange={(e) => setPaymentAmount(e.target.value)}
                      className="pl-8"
                      placeholder="0.00"
                      required
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Maximum: {formatPrice(layBuyOrder.remainingAmount)}
                  </p>
                </div>

                <div>
                  <Label htmlFor="paymentMethod">Payment Method</Label>
                  <Select value={paymentMethod} onValueChange={setPaymentMethod} required>
                    <SelectTrigger>
                      <SelectValue placeholder="Select payment method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="mpesa">M-Pesa</SelectItem>
                      <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                      <SelectItem value="cash_deposit">Cash Deposit</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="paymentProof">Payment Proof (Optional)</Label>
                  <div className="mt-1">
                    <Input
                      id="paymentProof"
                      type="file"
                      accept="image/*,.pdf"
                      onChange={handleFileUpload}
                      className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Upload receipt, screenshot, or proof of payment (Max 5MB)
                  </p>
                </div>

                <div>
                  <Label htmlFor="notes">Notes (Optional)</Label>
                  <Textarea
                    id="notes"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Add any additional notes about this payment..."
                    rows={3}
                  />
                </div>

                <Button type="submit" className="w-full" disabled={submitting}>
                  {submitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      <DollarSign className="h-4 w-4 mr-2" />
                      Submit Payment
                    </>
                  )}
                </Button>
              </form>
            )}
          </CardContent>
        </Card>

        {/* Order Summary */}
        <div className="space-y-6">
          {/* Payment Progress */}
          <Card>
            <CardHeader>
              <CardTitle>Payment Progress</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>{formatPrice(layBuyOrder.amountPaid)} / {formatPrice(layBuyOrder.layBuyAmount)}</span>
                </div>
                <Progress value={paymentProgress} className="h-3" />
                <div className="flex justify-between text-xs text-gray-600">
                  <span>{paymentProgress.toFixed(1)}% completed</span>
                  <span>{formatPrice(layBuyOrder.remainingAmount)} remaining</span>
                </div>
              </div>

              <Separator />

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Original Total:</span>
                  <span>{formatPrice(layBuyOrder.originalAmount)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Lay-Buy Amount (60%):</span>
                  <span className="font-medium">{formatPrice(layBuyOrder.layBuyAmount)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Amount Paid:</span>
                  <span className="text-green-600">{formatPrice(layBuyOrder.amountPaid)}</span>
                </div>
                <div className="flex justify-between font-medium">
                  <span>Remaining Balance:</span>
                  <span className="text-orange-600">{formatPrice(layBuyOrder.remainingAmount)}</span>
                </div>
              </div>

              {/* Due Date Info */}
              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="flex items-center gap-2 text-blue-700 mb-1">
                  <Calendar className="h-4 w-4" />
                  <span className="font-medium">Due Date</span>
                </div>
                <p className="text-sm text-blue-600">
                  {new Date(layBuyOrder.dueDate).toLocaleDateString()}
                </p>
                {timing.daysUntilDue > 0 ? (
                  <p className="text-xs text-blue-600 mt-1">
                    {timing.daysUntilDue} days remaining
                  </p>
                ) : timing.isInGracePeriod ? (
                  <p className="text-xs text-red-600 mt-1">
                    Grace period: {Math.abs(timing.daysUntilForfeiture)} days left
                  </p>
                ) : (
                  <p className="text-xs text-red-600 mt-1">
                    Overdue by {Math.abs(timing.daysUntilDue)} days
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Order Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Order Items
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {layBuyOrder.layBuyItems?.map((item, index) => (
                  <div key={index} className="flex justify-between items-start">
                    <div className="flex-1">
                      <h4 className="font-medium">{item.product.name}</h4>
                      {(item.size || item.color) && (
                        <p className="text-sm text-gray-600">
                          {item.size && `Size: ${item.size}`}
                          {item.size && item.color && " • "}
                          {item.color && `Color: ${item.color}`}
                        </p>
                      )}
                      <p className="text-sm text-gray-600">Qty: {item.quantity}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatPrice(item.layBuyPrice * item.quantity)}</p>
                      <p className="text-xs text-gray-500 line-through">
                        {formatPrice(item.originalPrice * item.quantity)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Customer Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserIcon className="h-5 w-5" />
                Delivery Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div className="flex items-start gap-2">
                <UserIcon className="h-4 w-4 text-gray-500 mt-0.5" />
                <span>{user.name}</span>
              </div>
              <div className="flex items-start gap-2">
                <Phone className="h-4 w-4 text-gray-500 mt-0.5" />
                <span>{layBuyOrder.phoneNumber}</span>
              </div>
              <div className="flex items-start gap-2">
                <MapPin className="h-4 w-4 text-gray-500 mt-0.5" />
                <span>{layBuyOrder.shippingAddress}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
