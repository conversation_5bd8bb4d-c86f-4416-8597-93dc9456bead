import { prisma } from "@/lib/prisma";
import { LayBuyOrder, LayBuyPayment, ApiResponse } from "@/utils/types";

/**
 * Get lay-buy orders for a user
 */
export async function getUserLayBuyOrders(
  userId: string,
  status?: string,
  page: number = 1,
  limit: number = 10
): Promise<ApiResponse<{ layBuyOrders: LayBuyOrder[]; pagination: any }>> {
  try {
    const skip = (page - 1) * limit;
    const where: any = { userId };
    
    if (status) {
      where.status = status;
    }

    const [layBuyOrders, total] = await Promise.all([
      prisma.layBuyOrder.findMany({
        where,
        include: {
          layBuyItems: {
            include: {
              product: true,
            },
          },
          layBuyPayments: {
            orderBy: { createdAt: "desc" },
          },
          layBuyReminders: {
            orderBy: { createdAt: "desc" },
            take: 1,
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.layBuyOrder.count({ where }),
    ]);

    return {
      success: true,
      data: {
        layBuyOrders: layBuyOrders as LayBuyOrder[],
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    };
  } catch (error) {
    console.error("Error fetching user lay-buy orders:", error);
    return {
      success: false,
      error: "Failed to fetch lay-buy orders",
    };
  }
}

/**
 * Get a specific lay-buy order
 */
export async function getLayBuyOrder(
  orderId: string,
  userId?: string
): Promise<ApiResponse<LayBuyOrder>> {
  try {
    const where: any = { id: orderId };
    if (userId) {
      where.userId = userId;
    }

    const layBuyOrder = await prisma.layBuyOrder.findFirst({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        layBuyItems: {
          include: {
            product: true,
          },
        },
        layBuyPayments: {
          orderBy: { createdAt: "desc" },
        },
        layBuyReminders: {
          orderBy: { createdAt: "desc" },
        },
      },
    });

    if (!layBuyOrder) {
      return {
        success: false,
        error: "Lay-Buy order not found",
      };
    }

    return {
      success: true,
      data: layBuyOrder as LayBuyOrder,
    };
  } catch (error) {
    console.error("Error fetching lay-buy order:", error);
    return {
      success: false,
      error: "Failed to fetch lay-buy order",
    };
  }
}

/**
 * Calculate days remaining for a lay-buy order
 */
export function calculateDaysRemaining(dueDate: Date, gracePeriodEnd: Date): {
  daysUntilDue: number;
  daysUntilForfeiture: number;
  isOverdue: boolean;
  isInGracePeriod: boolean;
  isForfeited: boolean;
} {
  const now = new Date();
  const daysUntilDue = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  const daysUntilForfeiture = Math.ceil((gracePeriodEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  
  const isOverdue = now > dueDate;
  const isInGracePeriod = isOverdue && now <= gracePeriodEnd;
  const isForfeited = now > gracePeriodEnd;

  return {
    daysUntilDue,
    daysUntilForfeiture,
    isOverdue,
    isInGracePeriod,
    isForfeited,
  };
}

/**
 * Get overdue lay-buy orders for reminder system
 */
export async function getOverdueLayBuyOrders(): Promise<ApiResponse<LayBuyOrder[]>> {
  try {
    const now = new Date();
    
    const overdueOrders = await prisma.layBuyOrder.findMany({
      where: {
        status: { in: ["ACTIVE", "EXTENDED"] },
        dueDate: { lt: now },
        gracePeriodEnd: { gt: now }, // Still within grace period
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        layBuyItems: {
          include: {
            product: true,
          },
        },
        layBuyPayments: {
          orderBy: { createdAt: "desc" },
        },
        layBuyReminders: {
          orderBy: { createdAt: "desc" },
        },
      },
    });

    return {
      success: true,
      data: overdueOrders as LayBuyOrder[],
    };
  } catch (error) {
    console.error("Error fetching overdue lay-buy orders:", error);
    return {
      success: false,
      error: "Failed to fetch overdue lay-buy orders",
    };
  }
}

/**
 * Get lay-buy orders that should be forfeited
 */
export async function getLayBuyOrdersToForfeit(): Promise<ApiResponse<LayBuyOrder[]>> {
  try {
    const now = new Date();
    
    const ordersToForfeit = await prisma.layBuyOrder.findMany({
      where: {
        status: { in: ["ACTIVE", "EXTENDED"] },
        gracePeriodEnd: { lt: now },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        layBuyItems: {
          include: {
            product: true,
          },
        },
      },
    });

    return {
      success: true,
      data: ordersToForfeit as LayBuyOrder[],
    };
  } catch (error) {
    console.error("Error fetching lay-buy orders to forfeit:", error);
    return {
      success: false,
      error: "Failed to fetch lay-buy orders to forfeit",
    };
  }
}

/**
 * Forfeit lay-buy orders and restore stock
 */
export async function forfeitLayBuyOrders(orderIds: string[]): Promise<ApiResponse<number>> {
  try {
    const result = await prisma.$transaction(async (tx) => {
      // Get orders to forfeit
      const orders = await tx.layBuyOrder.findMany({
        where: {
          id: { in: orderIds },
          status: { in: ["ACTIVE", "EXTENDED"] },
        },
        include: {
          layBuyItems: true,
        },
      });

      // Update orders to forfeited status
      await tx.layBuyOrder.updateMany({
        where: { id: { in: orderIds } },
        data: {
          status: "FORFEITED",
          forfeitedAt: new Date(),
          adminNotes: "Automatically forfeited due to non-payment",
        },
      });

      // Restore stock for all items
      for (const order of orders) {
        for (const item of order.layBuyItems) {
          await tx.product.update({
            where: { id: item.productId },
            data: {
              stock: {
                increment: item.quantity,
              },
            },
          });
        }
      }

      return orders.length;
    });

    return {
      success: true,
      data: result,
      message: `Successfully forfeited ${result} lay-buy order(s)`,
    };
  } catch (error) {
    console.error("Error forfeiting lay-buy orders:", error);
    return {
      success: false,
      error: "Failed to forfeit lay-buy orders",
    };
  }
}

/**
 * Get lay-buy summary statistics
 */
export async function getLayBuySummary(): Promise<ApiResponse<any>> {
  try {
    const [totalStats, statusStats, recentActivity] = await Promise.all([
      // Total statistics
      prisma.layBuyOrder.aggregate({
        _count: { id: true },
        _sum: {
          originalAmount: true,
          layBuyAmount: true,
          amountPaid: true,
          remainingAmount: true,
        },
      }),
      
      // Status breakdown
      prisma.layBuyOrder.groupBy({
        by: ["status"],
        _count: { id: true },
        _sum: {
          originalAmount: true,
          amountPaid: true,
          remainingAmount: true,
        },
      }),
      
      // Recent activity (last 30 days)
      prisma.layBuyOrder.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          },
        },
      }),
    ]);

    const summary = {
      total: {
        orders: totalStats._count.id || 0,
        value: totalStats._sum.originalAmount || 0,
        paid: totalStats._sum.amountPaid || 0,
        outstanding: totalStats._sum.remainingAmount || 0,
      },
      byStatus: statusStats.reduce((acc, stat) => {
        acc[stat.status] = {
          count: stat._count.id,
          value: stat._sum.originalAmount || 0,
          paid: stat._sum.amountPaid || 0,
          outstanding: stat._sum.remainingAmount || 0,
        };
        return acc;
      }, {} as Record<string, any>),
      recentActivity,
    };

    return {
      success: true,
      data: summary,
    };
  } catch (error) {
    console.error("Error fetching lay-buy summary:", error);
    return {
      success: false,
      error: "Failed to fetch lay-buy summary",
    };
  }
}
