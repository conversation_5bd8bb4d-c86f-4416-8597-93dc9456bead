import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { sendLayBuyWeeklyReminder } from "@/lib/email-service";
import { processAutomaticForfeiture } from "@/lib/lay-buy-forfeiture";

// This endpoint should be called by a cron job service (like Vercel Cron, GitHub Actions, or external cron)
// POST /api/cron/lay-buy-reminders
export async function POST(request: NextRequest) {
  try {
    // Verify the request is from an authorized source (optional security measure)
    const authHeader = request.headers.get("authorization");
    const expectedToken = process.env.CRON_SECRET_TOKEN;
    
    if (expectedToken && authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const results = {
      remindersSent: 0,
      ordersForfeited: 0,
      errors: [] as string[],
    };

    // Get all active lay-buy orders
    const activeOrders = await prisma.layBuyOrder.findMany({
      where: {
        status: { in: ["ACTIVE", "EXTENDED"] },
        remainingAmount: { gt: 0 },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        layBuyItems: {
          include: {
            product: true,
          },
        },
        layBuyReminders: {
          orderBy: { createdAt: "desc" },
          take: 1,
        },
      },
    });

    const now = new Date();

    for (const order of activeOrders) {
      try {
        const startDate = new Date(order.startDate);
        const dueDate = new Date(order.dueDate);
        const gracePeriodEnd = new Date(order.gracePeriodEnd);
        
        // Calculate which week we're in
        const daysSinceStart = Math.floor((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
        const weekNumber = Math.ceil(daysSinceStart / 7);
        
        // Skip orders that are past grace period (will be handled by forfeiture process)
        if (now > gracePeriodEnd) {
          continue;
        }

        // Check if we should send a reminder
        const lastReminder = order.layBuyReminders[0];
        const daysSinceLastReminder = lastReminder 
          ? Math.floor((now.getTime() - new Date(lastReminder.createdAt).getTime()) / (1000 * 60 * 60 * 24))
          : 999; // If no reminder sent, treat as if it's been a long time

        // Send reminders based on schedule
        let shouldSendReminder = false;
        let reminderType = "weekly";

        if (now > gracePeriodEnd) {
          // Don't send reminders if past grace period (will be forfeited)
          continue;
        } else if (now > dueDate) {
          // Grace period - send daily reminders
          shouldSendReminder = daysSinceLastReminder >= 1;
          reminderType = "grace_period";
        } else if (weekNumber === 6 || (now.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24) <= 3) {
          // Final warning - send every 2 days in the last week
          shouldSendReminder = daysSinceLastReminder >= 2;
          reminderType = "final_warning";
        } else if (weekNumber === 5 || (now.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24) <= 7) {
          // Urgent - send every 3 days
          shouldSendReminder = daysSinceLastReminder >= 3;
          reminderType = "urgent";
        } else if (weekNumber >= 1) {
          // Regular weekly reminders
          shouldSendReminder = daysSinceLastReminder >= 7;
          reminderType = "weekly";
        }

        if (shouldSendReminder) {
          // Send reminder email
          await sendLayBuyWeeklyReminder(order as any, weekNumber);

          // Record the reminder
          await prisma.layBuyReminder.create({
            data: {
              layBuyOrderId: order.id,
              weekNumber: Math.min(weekNumber, 7), // Cap at 7 for grace period
              emailSent: true,
              reminderType,
            },
          });

          results.remindersSent++;
        }
      } catch (error) {
        console.error(`Error processing order ${order.orderNumber}:`, error);
        results.errors.push(`Order ${order.orderNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // Process automatic forfeiture for overdue orders
    try {
      const forfeitResult = await processAutomaticForfeiture();
      results.ordersForfeited = forfeitResult.forfeitedOrders;
      if (!forfeitResult.success && forfeitResult.errors.length > 0) {
        results.errors.push(...forfeitResult.errors);
      }
    } catch (error) {
      console.error("Error in automatic forfeiture:", error);
      results.errors.push(`Forfeiture error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Send admin notification if there were significant activities
    if (results.remindersSent > 0 || results.ordersForfeited > 0) {
      try {
        // TODO: Send admin summary email
        console.log("Lay-Buy cron job completed:", results);
      } catch (error) {
        console.error("Error sending admin notification:", error);
      }
    }

    return NextResponse.json({
      success: true,
      data: results,
      message: `Processed ${activeOrders.length} orders. Sent ${results.remindersSent} reminders, forfeited ${results.ordersForfeited} orders.`,
    });

  } catch (error) {
    console.error("Error in lay-buy reminders cron job:", error);
    return NextResponse.json(
      { 
        success: false, 
        error: "Failed to process lay-buy reminders",
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET endpoint for manual testing (remove in production)
export async function GET(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV === "production") {
    return NextResponse.json(
      { success: false, error: "Not available in production" },
      { status: 404 }
    );
  }

  // Call the POST handler for testing
  return POST(request);
}
