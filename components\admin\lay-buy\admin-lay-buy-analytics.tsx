"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  AlertTriangle,
  Download,
  Calendar,
  RefreshCw,
  PieChart,
  Activity
} from "lucide-react";
import { formatPrice } from "@/lib/product-utils";

interface AnalyticsData {
  overview: {
    totalOrders: number;
    totalValue: number;
    totalPaid: number;
    totalOutstanding: number;
    averageOrderValue: number;
    completionRate: number;
    forfeitureRate: number;
  };
  statusBreakdown: {
    active: number;
    completed: number;
    cancelled: number;
    forfeited: number;
    extended: number;
  };
  financialMetrics: {
    totalRevenue: number;
    totalForfeited: number;
    totalRefunded: number;
    averagePaymentTime: number;
    collectionEfficiency: number;
  };
  riskMetrics: {
    overdueOrders: number;
    gracePeriodOrders: number;
    highRiskCustomers: number;
    averageDaysToComplete: number;
  };
  customerInsights: {
    totalCustomers: number;
    repeatCustomers: number;
    averageOrdersPerCustomer: number;
    topCustomers: Array<{
      customerId: string;
      customerName: string;
      totalOrders: number;
      totalValue: number;
      completionRate: number;
    }>;
  };
}

export default function AdminLayBuyAnalytics() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState("30"); // days
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [reportType, setReportType] = useState("analytics");

  useEffect(() => {
    fetchAnalytics();
  }, [dateRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        type: reportType,
      });

      if (dateRange === "custom" && startDate && endDate) {
        params.append("startDate", startDate);
        params.append("endDate", endDate);
      } else if (dateRange !== "all") {
        const end = new Date();
        const start = new Date();
        start.setDate(start.getDate() - parseInt(dateRange));
        params.append("startDate", start.toISOString());
        params.append("endDate", end.toISOString());
      }

      const response = await fetch(`/api/admin/lay-buy/analytics?${params}`);
      const result = await response.json();

      if (result.success) {
        setAnalytics(result.data);
      } else {
        setError(result.error || "Failed to fetch analytics");
      }
    } catch (err) {
      setError("Failed to load analytics");
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (format: "csv" | "json") => {
    try {
      const params = new URLSearchParams({
        type: "export",
        format,
      });

      if (dateRange === "custom" && startDate && endDate) {
        params.append("startDate", startDate);
        params.append("endDate", endDate);
      } else if (dateRange !== "all") {
        const end = new Date();
        const start = new Date();
        start.setDate(start.getDate() - parseInt(dateRange));
        params.append("startDate", start.toISOString());
        params.append("endDate", end.toISOString());
      }

      const response = await fetch(`/api/admin/lay-buy/analytics?${params}`);
      
      if (format === "csv") {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `lay-buy-export-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        const result = await response.json();
        const dataStr = JSON.stringify(result.data, null, 2);
        const blob = new Blob([dataStr], { type: "application/json" });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `lay-buy-export-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (err) {
      setError("Failed to export data");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert className="border-red-200 bg-red-50">
        <AlertTriangle className="h-4 w-4 text-red-600" />
        <AlertDescription className="text-red-700">{error}</AlertDescription>
      </Alert>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center py-8">
        <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Analytics Data</h3>
        <p className="text-gray-600">Analytics data will appear here once Lay-Buy orders are created.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Lay-Buy Analytics</h2>
          <p className="text-gray-600">Performance insights and reporting</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={fetchAnalytics}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" onClick={() => handleExport("csv")}>
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Button variant="outline" onClick={() => handleExport("json")}>
            <Download className="h-4 w-4 mr-2" />
            Export JSON
          </Button>
        </div>
      </div>

      {/* Date Range Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4 items-end">
            <div>
              <Label htmlFor="dateRange">Date Range</Label>
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">Last 7 days</SelectItem>
                  <SelectItem value="30">Last 30 days</SelectItem>
                  <SelectItem value="90">Last 90 days</SelectItem>
                  <SelectItem value="365">Last year</SelectItem>
                  <SelectItem value="custom">Custom range</SelectItem>
                  <SelectItem value="all">All time</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {dateRange === "custom" && (
              <>
                <div>
                  <Label htmlFor="startDate">Start Date</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="endDate">End Date</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                  />
                </div>
              </>
            )}

            <Button onClick={fetchAnalytics}>
              <Calendar className="h-4 w-4 mr-2" />
              Apply
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Orders</p>
                <p className="text-2xl font-bold">{analytics.overview.totalOrders}</p>
              </div>
              <Activity className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Value</p>
                <p className="text-2xl font-bold">{formatPrice(analytics.overview.totalValue)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Completion Rate</p>
                <p className="text-2xl font-bold">{analytics.overview.completionRate.toFixed(1)}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Forfeiture Rate</p>
                <p className="text-2xl font-bold">{analytics.overview.forfeitureRate.toFixed(1)}%</p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics Tabs */}
      <Tabs defaultValue="financial" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="financial">Financial</TabsTrigger>
          <TabsTrigger value="status">Status</TabsTrigger>
          <TabsTrigger value="risk">Risk</TabsTrigger>
          <TabsTrigger value="customers">Customers</TabsTrigger>
        </TabsList>

        {/* Financial Tab */}
        <TabsContent value="financial">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Total Revenue:</span>
                  <span className="font-medium text-green-600">
                    {formatPrice(analytics.financialMetrics.totalRevenue)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Total Forfeited:</span>
                  <span className="font-medium text-red-600">
                    {formatPrice(analytics.financialMetrics.totalForfeited)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Total Refunded:</span>
                  <span className="font-medium text-yellow-600">
                    {formatPrice(analytics.financialMetrics.totalRefunded)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Collection Efficiency:</span>
                  <span className="font-medium">
                    {analytics.financialMetrics.collectionEfficiency.toFixed(1)}%
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Payment Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Average Payment Time:</span>
                  <span className="font-medium">
                    {analytics.financialMetrics.averagePaymentTime.toFixed(1)} days
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Average Order Value:</span>
                  <span className="font-medium">
                    {formatPrice(analytics.overview.averageOrderValue)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Total Outstanding:</span>
                  <span className="font-medium text-orange-600">
                    {formatPrice(analytics.overview.totalOutstanding)}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Status Tab */}
        <TabsContent value="status">
          <Card>
            <CardHeader>
              <CardTitle>Order Status Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {analytics.statusBreakdown.active}
                  </div>
                  <div className="text-sm text-gray-600">Active</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {analytics.statusBreakdown.completed}
                  </div>
                  <div className="text-sm text-gray-600">Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-600">
                    {analytics.statusBreakdown.cancelled}
                  </div>
                  <div className="text-sm text-gray-600">Cancelled</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {analytics.statusBreakdown.forfeited}
                  </div>
                  <div className="text-sm text-gray-600">Forfeited</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">
                    {analytics.statusBreakdown.extended}
                  </div>
                  <div className="text-sm text-gray-600">Extended</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Risk Tab */}
        <TabsContent value="risk">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                  Risk Alerts
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {analytics.riskMetrics.overdueOrders > 0 && (
                  <Alert className="border-red-200 bg-red-50">
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-red-700">
                      {analytics.riskMetrics.overdueOrders} orders are currently overdue
                    </AlertDescription>
                  </Alert>
                )}
                
                {analytics.riskMetrics.gracePeriodOrders > 0 && (
                  <Alert className="border-yellow-200 bg-yellow-50">
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    <AlertDescription className="text-yellow-700">
                      {analytics.riskMetrics.gracePeriodOrders} orders are in grace period
                    </AlertDescription>
                  </Alert>
                )}

                {analytics.riskMetrics.overdueOrders === 0 && analytics.riskMetrics.gracePeriodOrders === 0 && (
                  <div className="text-center py-4 text-green-600">
                    ✓ No immediate risk alerts
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Risk Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Overdue Orders:</span>
                  <Badge variant={analytics.riskMetrics.overdueOrders > 0 ? "destructive" : "secondary"}>
                    {analytics.riskMetrics.overdueOrders}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Grace Period Orders:</span>
                  <Badge variant={analytics.riskMetrics.gracePeriodOrders > 0 ? "secondary" : "outline"}>
                    {analytics.riskMetrics.gracePeriodOrders}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Avg. Days to Complete:</span>
                  <span className="font-medium">
                    {analytics.riskMetrics.averageDaysToComplete.toFixed(1)} days
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Customers Tab */}
        <TabsContent value="customers">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Customer Overview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Total Customers:</span>
                  <span className="font-medium">{analytics.customerInsights.totalCustomers}</span>
                </div>
                <div className="flex justify-between">
                  <span>Repeat Customers:</span>
                  <span className="font-medium">{analytics.customerInsights.repeatCustomers}</span>
                </div>
                <div className="flex justify-between">
                  <span>Avg. Orders per Customer:</span>
                  <span className="font-medium">
                    {analytics.customerInsights.averageOrdersPerCustomer.toFixed(1)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Retention Rate:</span>
                  <span className="font-medium">
                    {analytics.customerInsights.totalCustomers > 0 
                      ? ((analytics.customerInsights.repeatCustomers / analytics.customerInsights.totalCustomers) * 100).toFixed(1)
                      : 0}%
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Customers</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analytics.customerInsights.topCustomers.slice(0, 5).map((customer, index) => (
                    <div key={customer.customerId} className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">{customer.customerName}</div>
                        <div className="text-sm text-gray-600">
                          {customer.totalOrders} orders • {customer.completionRate.toFixed(1)}% completion
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{formatPrice(customer.totalValue)}</div>
                        <div className="text-sm text-gray-600">#{index + 1}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
