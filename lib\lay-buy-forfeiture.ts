import { prisma } from "@/lib/prisma";
import { LayBuyOrder } from "@/utils/types";
import { sendEmail } from "@/lib/email";

export interface ForfeitureResult {
  success: boolean;
  forfeitedOrders: number;
  restoredStock: number;
  errors: string[];
  details: Array<{
    orderId: string;
    orderNumber: string;
    customerEmail: string;
    amountForfeited: number;
    itemsRestored: number;
  }>;
}

export interface RefundCalculation {
  isEligible: boolean;
  refundAmount: number;
  refundPercentage: number;
  reason: string;
  processingFee?: number;
}

/**
 * Calculate refund amount based on cancellation timing
 */
export function calculateRefund(
  layBuyOrder: LayBuyOrder,
  cancellationDate: Date = new Date()
): RefundCalculation {
  const startDate = new Date(layBuyOrder.startDate);
  const dueDate = new Date(layBuyOrder.dueDate);
  const gracePeriodEnd = new Date(layBuyOrder.gracePeriodEnd);
  const amountPaid = layBuyOrder.amountPaid;

  // Check if cancellation is within the 6-week period
  const isWithinPeriod = cancellationDate <= dueDate;
  const isInGracePeriod = cancellationDate > dueDate && cancellationDate <= gracePeriodEnd;
  const isAfterGracePeriod = cancellationDate > gracePeriodEnd;

  if (isAfterGracePeriod) {
    // No refund after grace period
    return {
      isEligible: false,
      refundAmount: 0,
      refundPercentage: 0,
      reason: "Cancellation after grace period - no refund available",
    };
  }

  if (isInGracePeriod) {
    // No refund during grace period
    return {
      isEligible: false,
      refundAmount: 0,
      refundPercentage: 0,
      reason: "Cancellation during grace period - no refund available",
    };
  }

  if (isWithinPeriod) {
    // 50% refund if cancelled within the 6-week period
    const refundAmount = amountPaid * 0.5;
    return {
      isEligible: true,
      refundAmount,
      refundPercentage: 50,
      reason: "Early cancellation - 50% refund eligible",
    };
  }

  // Default case (shouldn't reach here)
  return {
    isEligible: false,
    refundAmount: 0,
    refundPercentage: 0,
    reason: "Unable to determine refund eligibility",
  };
}

/**
 * Process automatic forfeiture for overdue orders
 */
export async function processAutomaticForfeiture(): Promise<ForfeitureResult> {
  const result: ForfeitureResult = {
    success: false,
    forfeitedOrders: 0,
    restoredStock: 0,
    errors: [],
    details: [],
  };

  try {
    const now = new Date();

    // Find orders that should be forfeited (past grace period)
    const ordersToForfeit = await prisma.layBuyOrder.findMany({
      where: {
        status: { in: ["ACTIVE", "EXTENDED"] },
        gracePeriodEnd: { lt: now },
        remainingAmount: { gt: 0 },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        layBuyItems: {
          include: {
            product: true,
          },
        },
      },
    });

    if (ordersToForfeit.length === 0) {
      result.success = true;
      return result;
    }

    // Process each order for forfeiture
    for (const order of ordersToForfeit) {
      try {
        await prisma.$transaction(async (tx) => {
          // Update order status to forfeited
          await tx.layBuyOrder.update({
            where: { id: order.id },
            data: {
              status: "FORFEITED",
              forfeitedAt: now,
              adminNotes: `Automatically forfeited on ${now.toISOString()} due to non-payment`,
            },
          });

          // Restore stock for all items
          let itemsRestored = 0;
          for (const item of order.layBuyItems) {
            await tx.product.update({
              where: { id: item.productId },
              data: {
                stock: {
                  increment: item.quantity,
                },
              },
            });
            itemsRestored += item.quantity;
          }

          result.details.push({
            orderId: order.id,
            orderNumber: order.orderNumber,
            customerEmail: order.user?.email || "",
            amountForfeited: order.amountPaid,
            itemsRestored,
          });

          result.forfeitedOrders++;
          result.restoredStock += itemsRestored;
        });

        // Send forfeiture notification email
        try {
          await sendForfeitureNotificationEmail(order as any);
        } catch (emailError) {
          console.error(`Failed to send forfeiture email for order ${order.orderNumber}:`, emailError);
          result.errors.push(`Email notification failed for order ${order.orderNumber}`);
        }
      } catch (error) {
        console.error(`Failed to forfeit order ${order.orderNumber}:`, error);
        result.errors.push(`Failed to forfeit order ${order.orderNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    result.success = result.errors.length === 0;
    return result;
  } catch (error) {
    console.error("Error in automatic forfeiture process:", error);
    result.errors.push(`System error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return result;
  }
}

/**
 * Process manual cancellation with refund calculation
 */
export async function processCancellation(
  layBuyOrderId: string,
  reason: string = "Customer cancellation",
  adminUserId?: string
): Promise<{
  success: boolean;
  refundCalculation: RefundCalculation;
  error?: string;
}> {
  try {
    // Get the order
    const order = await prisma.layBuyOrder.findUnique({
      where: { id: layBuyOrderId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        layBuyItems: {
          include: {
            product: true,
          },
        },
      },
    });

    if (!order) {
      return {
        success: false,
        refundCalculation: {
          isEligible: false,
          refundAmount: 0,
          refundPercentage: 0,
          reason: "Order not found",
        },
        error: "Order not found",
      };
    }

    if (order.status !== "ACTIVE" && order.status !== "EXTENDED") {
      return {
        success: false,
        refundCalculation: {
          isEligible: false,
          refundAmount: 0,
          refundPercentage: 0,
          reason: "Order cannot be cancelled in current status",
        },
        error: "Order cannot be cancelled in current status",
      };
    }

    // Calculate refund
    const refundCalculation = calculateRefund(order as any);

    // Process cancellation
    await prisma.$transaction(async (tx) => {
      // Update order status
      await tx.layBuyOrder.update({
        where: { id: layBuyOrderId },
        data: {
          status: "CANCELLED",
          cancelledAt: new Date(),
          refundAmount: refundCalculation.refundAmount,
          adminNotes: `${reason}. ${refundCalculation.reason}`,
        },
      });

      // Restore stock
      for (const item of order.layBuyItems) {
        await tx.product.update({
          where: { id: item.productId },
          data: {
            stock: {
              increment: item.quantity,
            },
          },
        });
      }
    });

    // Send cancellation confirmation email
    try {
      await sendCancellationConfirmationEmail(order as any, refundCalculation);
    } catch (emailError) {
      console.error("Failed to send cancellation email:", emailError);
    }

    return {
      success: true,
      refundCalculation,
    };
  } catch (error) {
    console.error("Error processing cancellation:", error);
    return {
      success: false,
      refundCalculation: {
        isEligible: false,
        refundAmount: 0,
        refundPercentage: 0,
        reason: "System error",
      },
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Send forfeiture notification email
 */
async function sendForfeitureNotificationEmail(order: LayBuyOrder) {
  const subject = `Lay-Buy Order Forfeited - #${order.orderNumber}`;
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Lay-Buy Order Forfeited</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #dc2626; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #fef2f2; padding: 30px; border-radius: 0 0 8px 8px; }
        .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Order Forfeited</h1>
        </div>
        <div class="content">
          <h2>Hi ${order.user?.name || "Customer"},</h2>
          <p>We regret to inform you that your Lay-Buy order <strong>#${order.orderNumber}</strong> has been forfeited due to non-payment within the grace period.</p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>Order Details:</h3>
            <p><strong>Order Number:</strong> ${order.orderNumber}</p>
            <p><strong>Amount Paid:</strong> M ${order.amountPaid.toFixed(2)}</p>
            <p><strong>Amount Forfeited:</strong> M ${order.amountPaid.toFixed(2)}</p>
            <p><strong>Due Date:</strong> ${new Date(order.dueDate).toLocaleDateString()}</p>
            <p><strong>Grace Period Ended:</strong> ${new Date(order.gracePeriodEnd).toLocaleDateString()}</p>
          </div>
          
          <p>As per our Lay-Buy terms and conditions, payment not received within the grace period results in forfeiture of the order and all payments made.</p>
          
          <p>The items from this order have been returned to our inventory and are available for purchase.</p>
          
          <p>If you believe this forfeiture was made in error, please contact our customer service team immediately.</p>
        </div>
        <div class="footer">
          <p>Rivv E-commerce | <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
    Lay-Buy Order Forfeited
    
    Hi ${order.user?.name || "Customer"},
    
    Your Lay-Buy order #${order.orderNumber} has been forfeited due to non-payment within the grace period.
    
    Order Details:
    - Order Number: ${order.orderNumber}
    - Amount Paid: M ${order.amountPaid.toFixed(2)}
    - Amount Forfeited: M ${order.amountPaid.toFixed(2)}
    - Due Date: ${new Date(order.dueDate).toLocaleDateString()}
    - Grace Period Ended: ${new Date(order.gracePeriodEnd).toLocaleDateString()}
    
    As per our terms, payment not received within the grace period results in forfeiture.
    
    Contact us if you believe this was made in error: <EMAIL>
  `;

  await sendEmail({
    to: order.user?.email || "",
    subject,
    html,
    text,
  });
}

/**
 * Send cancellation confirmation email
 */
async function sendCancellationConfirmationEmail(order: LayBuyOrder, refundCalculation: RefundCalculation) {
  const subject = `Lay-Buy Order Cancelled - #${order.orderNumber}`;
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Lay-Buy Order Cancelled</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #6b7280; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
        .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Order Cancelled</h1>
        </div>
        <div class="content">
          <h2>Hi ${order.user?.name || "Customer"},</h2>
          <p>Your Lay-Buy order <strong>#${order.orderNumber}</strong> has been successfully cancelled.</p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>Cancellation Details:</h3>
            <p><strong>Order Number:</strong> ${order.orderNumber}</p>
            <p><strong>Amount Paid:</strong> M ${order.amountPaid.toFixed(2)}</p>
            <p><strong>Refund Amount:</strong> M ${refundCalculation.refundAmount.toFixed(2)}</p>
            <p><strong>Refund Percentage:</strong> ${refundCalculation.refundPercentage}%</p>
            <p><strong>Reason:</strong> ${refundCalculation.reason}</p>
          </div>
          
          ${refundCalculation.isEligible ? `
            <p>Your refund of M ${refundCalculation.refundAmount.toFixed(2)} will be processed within 7-14 business days to your original payment method.</p>
          ` : `
            <p>Unfortunately, no refund is available for this cancellation due to the timing of the cancellation request.</p>
          `}
          
          <p>The items from this order have been returned to our inventory.</p>
          
          <p>Thank you for your understanding.</p>
        </div>
        <div class="footer">
          <p>Rivv E-commerce | <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
      </div>
    </body>
    </html>
  `;

  await sendEmail({
    to: order.user?.email || "",
    subject,
    html,
    text: `Order #${order.orderNumber} cancelled. Refund: M ${refundCalculation.refundAmount.toFixed(2)}`,
  });
}
