import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-client";
import { prisma } from "@/lib/prisma";
import { ApiResponse } from "@/utils/types";
import { sendLayBuyConfirmationEmail } from "@/lib/email-service";
import { formatPrice } from "@/lib/product-utils";

// Generate unique lay-buy order number
function generateLayBuyOrderNumber(): string {
  const timestamp = Date.now().toString();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `LB${timestamp.slice(-6)}${random}`;
}

// Calculate due dates
function calculateDueDates() {
  const startDate = new Date();
  const dueDate = new Date(startDate);
  dueDate.setDate(dueDate.getDate() + 42); // 6 weeks
  
  const gracePeriodEnd = new Date(dueDate);
  gracePeriodEnd.setDate(gracePeriodEnd.getDate() + 7); // 1 week grace period
  
  return { startDate, dueDate, gracePeriodEnd };
}

// POST /api/lay-buy/orders - Create new lay-buy order
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      shippingAddress,
      phoneNumber,
      notes,
      discountAmount = 0,
      discountCode,
      paymentMethod,
      paymentProofUrl,
      originalAmount,
      layBuyAmount,
      termsAccepted,
      items,
    } = body;

    // Validate required fields
    if (!shippingAddress || !phoneNumber || !items || items.length === 0) {
      return NextResponse.json(
        { success: false, error: "Missing required fields" },
        { status: 400 }
      );
    }

    if (!termsAccepted) {
      return NextResponse.json(
        { success: false, error: "Terms and conditions must be accepted" },
        { status: 400 }
      );
    }

    // Validate minimum amount for lay-buy
    if (originalAmount < 700) {
      return NextResponse.json(
        { success: false, error: "Lay-Buy is only available for orders of M 700.00 and above" },
        { status: 400 }
      );
    }

    // Check if user has too many active lay-buys (max 3)
    const activeLayBuys = await prisma.layBuyOrder.count({
      where: {
        userId: user.id,
        status: { in: ['ACTIVE', 'EXTENDED'] }
      }
    });

    if (activeLayBuys >= 3) {
      return NextResponse.json(
        { success: false, error: "You can have a maximum of 3 active Lay-Buy orders" },
        { status: 400 }
      );
    }

    // Validate products and calculate amounts
    let calculatedOriginalAmount = 0;
    const layBuyItems: any[] = [];

    for (const item of items) {
      const product = await prisma.product.findUnique({
        where: { id: item.productId, isActive: true },
      });

      if (!product) {
        return NextResponse.json(
          { success: false, error: `Product ${item.productId} not found` },
          { status: 400 }
        );
      }

      if (product.stock < item.quantity) {
        return NextResponse.json(
          { success: false, error: `Insufficient stock for ${product.name}` },
          { status: 400 }
        );
      }

      const originalPrice = item.price;
      const layBuyPrice = originalPrice * 0.6; // 60% of original price
      const itemTotal = originalPrice * item.quantity;
      calculatedOriginalAmount += itemTotal;

      layBuyItems.push({
        productId: item.productId,
        quantity: item.quantity,
        originalPrice: originalPrice,
        layBuyPrice: layBuyPrice,
        size: item.size,
        color: item.color,
      });
    }

    // Verify amounts match
    const calculatedLayBuyAmount = calculatedOriginalAmount * 0.6;
    if (Math.abs(calculatedLayBuyAmount - layBuyAmount) > 0.01) {
      return NextResponse.json(
        { success: false, error: "Amount calculation mismatch" },
        { status: 400 }
      );
    }

    const { startDate, dueDate, gracePeriodEnd } = calculateDueDates();

    // Create lay-buy order in transaction
    const result = await prisma.$transaction(async (tx) => {
      // Generate unique order number
      const orderNumber = generateLayBuyOrderNumber();

      // Clear user's cart
      await tx.cartItem.deleteMany({
        where: { userId: user.id },
      });

      // Reserve products (reduce stock)
      const stockUpdates = items.map((item: any) =>
        tx.product.update({
          where: { id: item.productId },
          data: {
            stock: {
              decrement: item.quantity,
            },
          },
        })
      );
      await Promise.all(stockUpdates);

      // Create lay-buy order
      const layBuyOrder = await tx.layBuyOrder.create({
        data: {
          userId: user.id,
          orderNumber,
          status: "ACTIVE",
          originalAmount: calculatedOriginalAmount,
          layBuyAmount: calculatedLayBuyAmount,
          amountPaid: calculatedLayBuyAmount, // Initial payment
          remainingAmount: calculatedOriginalAmount - calculatedLayBuyAmount,
          shippingAddress,
          phoneNumber,
          notes,
          startDate,
          dueDate,
          gracePeriodEnd,
          termsAccepted: true,
          termsAcceptedAt: new Date(),
          layBuyItems: {
            create: layBuyItems,
          },
        },
        include: {
          layBuyItems: {
            include: {
              product: true,
            },
          },
        },
      });

      // Create initial payment record
      if (paymentProofUrl) {
        await tx.layBuyPayment.create({
          data: {
            layBuyOrderId: layBuyOrder.id,
            amount: calculatedLayBuyAmount,
            status: "PENDING",
            paymentMethod: paymentMethod,
            paymentProof: paymentProofUrl,
            notes: "Initial Lay-Buy payment (60%)",
          },
        });
      }

      return layBuyOrder;
    });

    // Send confirmation email
    try {
      await sendLayBuyConfirmationEmail(result as any);
    } catch (error) {
      console.error("Failed to send lay-buy confirmation email:", error);
    }

    const response: ApiResponse<typeof result> = {
      success: true,
      data: result,
      message: "Lay-Buy order created successfully",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error creating lay-buy order:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create lay-buy order" },
      { status: 500 }
    );
  }
}

// GET /api/lay-buy/orders - Get user's lay-buy orders
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get("status");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const skip = (page - 1) * limit;

    const where: any = { userId: user.id };
    if (status) {
      where.status = status;
    }

    const [layBuyOrders, total] = await Promise.all([
      prisma.layBuyOrder.findMany({
        where,
        include: {
          layBuyItems: {
            include: {
              product: true,
            },
          },
          layBuyPayments: {
            orderBy: { createdAt: "desc" },
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.layBuyOrder.count({ where }),
    ]);

    const response: ApiResponse = {
      success: true,
      data: {
        layBuyOrders,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching lay-buy orders:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch lay-buy orders" },
      { status: 500 }
    );
  }
}

// Admin endpoints would be in a separate file: /api/admin/lay-buy/orders/route.ts
