import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 10);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Admin User',
      email: '<EMAIL>',
      password: adminPassword,
      role: 'ADMIN',
      emailVerified: true,
    },
  });

  // Create test customer users
  const customerPassword = await bcrypt.hash('customer123', 10);
  const customer1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: '<PERSON>',
      email: '<EMAIL>',
      password: customerPassword,
      role: 'USER',
      emailVerified: true,
    },
  });

  const customer2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: '<PERSON>',
      email: '<EMAIL>',
      password: customerPassword,
      role: 'USER',
      emailVerified: true,
    },
  });

  console.log('✅ Users created');

  // Create categories
  const menCategory = await prisma.category.upsert({
    where: { name: 'Men' },
    update: {},
    create: {
      name: 'Men',
      description: 'Footwear for men',
      isActive: true,
    },
  });

  const womenCategory = await prisma.category.upsert({
    where: { name: 'Women' },
    update: {},
    create: {
      name: 'Women',
      description: 'Footwear for women',
      isActive: true,
    },
  });

  const kidsCategory = await prisma.category.upsert({
    where: { name: 'Kids' },
    update: {},
    create: {
      name: 'Kids',
      description: 'Footwear for children',
      isActive: true,
    },
  });

  const sportsCategory = await prisma.category.upsert({
    where: { name: 'Sports' },
    update: {},
    create: {
      name: 'Sports',
      description: 'Athletic and sports footwear',
      isActive: true,
    },
  });

  console.log('✅ Categories created');

  // Create products
  const products = [
    {
      name: 'Nike Air Max 270',
      description: 'Comfortable running shoes with excellent cushioning and modern design.',
      price: 1299.99,
      discountedPrice: 999.99,
      stock: 50,
      categoryId: menCategory.id,
      images: ['/api/placeholder/400/400'],
      sizes: ['7', '8', '9', '10', '11', '12'],
      colors: ['Black', 'White', 'Blue'],
      isActive: true,
      isFeatured: true,
    },
    {
      name: 'Adidas Ultraboost 22',
      description: 'Premium running shoes with responsive cushioning and energy return.',
      price: 1599.99,
      discountedPrice: 1299.99,
      stock: 35,
      categoryId: menCategory.id,
      images: ['/api/placeholder/400/400'],
      sizes: ['7', '8', '9', '10', '11', '12'],
      colors: ['Black', 'White', 'Grey'],
      isActive: true,
      isFeatured: true,
    },
    {
      name: 'Converse Chuck Taylor All Star',
      description: 'Classic canvas sneakers that never go out of style.',
      price: 799.99,
      stock: 75,
      categoryId: womenCategory.id,
      images: ['/api/placeholder/400/400'],
      sizes: ['5', '6', '7', '8', '9', '10'],
      colors: ['Black', 'White', 'Red', 'Pink'],
      isActive: true,
      isFeatured: false,
    },
    {
      name: 'Vans Old Skool',
      description: 'Iconic skate shoes with durable construction and timeless style.',
      price: 899.99,
      discountedPrice: 749.99,
      stock: 60,
      categoryId: womenCategory.id,
      images: ['/api/placeholder/400/400'],
      sizes: ['5', '6', '7', '8', '9', '10'],
      colors: ['Black', 'White', 'Checkered'],
      isActive: true,
      isFeatured: true,
    },
    {
      name: 'Puma RS-X',
      description: 'Retro-inspired running shoes with bold design and comfortable fit.',
      price: 1199.99,
      stock: 40,
      categoryId: sportsCategory.id,
      images: ['/api/placeholder/400/400'],
      sizes: ['7', '8', '9', '10', '11', '12'],
      colors: ['Multi', 'Black', 'White'],
      isActive: true,
      isFeatured: false,
    },
    {
      name: 'New Balance 990v5',
      description: 'Premium lifestyle sneakers made in USA with superior craftsmanship.',
      price: 1799.99,
      discountedPrice: 1499.99,
      stock: 25,
      categoryId: menCategory.id,
      images: ['/api/placeholder/400/400'],
      sizes: ['7', '8', '9', '10', '11', '12'],
      colors: ['Grey', 'Navy', 'Black'],
      isActive: true,
      isFeatured: true,
    },
    {
      name: 'Kids Nike Air Force 1',
      description: 'Classic basketball shoes sized for kids with durable construction.',
      price: 699.99,
      stock: 80,
      categoryId: kidsCategory.id,
      images: ['/api/placeholder/400/400'],
      sizes: ['1', '2', '3', '4', '5', '6'],
      colors: ['White', 'Black', 'Pink', 'Blue'],
      isActive: true,
      isFeatured: true,
    },
    {
      name: 'Reebok Classic Leather',
      description: 'Timeless leather sneakers with clean lines and comfortable fit.',
      price: 899.99,
      discountedPrice: 699.99,
      stock: 55,
      categoryId: womenCategory.id,
      images: ['/api/placeholder/400/400'],
      sizes: ['5', '6', '7', '8', '9', '10'],
      colors: ['White', 'Black', 'Brown'],
      isActive: true,
      isFeatured: false,
    },
    {
      name: 'Jordan 1 Retro High',
      description: 'Iconic basketball shoes with premium materials and classic design.',
      price: 1899.99,
      stock: 30,
      categoryId: menCategory.id,
      images: ['/api/placeholder/400/400'],
      sizes: ['7', '8', '9', '10', '11', '12'],
      colors: ['Black/Red', 'White/Black', 'Royal Blue'],
      isActive: true,
      isFeatured: true,
    },
    {
      name: 'Asics Gel-Kayano 29',
      description: 'Stability running shoes with advanced cushioning technology.',
      price: 1399.99,
      discountedPrice: 1199.99,
      stock: 45,
      categoryId: sportsCategory.id,
      images: ['/api/placeholder/400/400'],
      sizes: ['7', '8', '9', '10', '11', '12'],
      colors: ['Black', 'Blue', 'Grey'],
      isActive: true,
      isFeatured: false,
    },
  ];

  const createdProducts = [];
  for (const productData of products) {
    const product = await prisma.product.create({
      data: productData,
    });
    createdProducts.push(product);
  }

  console.log('✅ Products created');

  // Create discount codes
  await prisma.discountCode.createMany({
    data: [
      {
        code: 'WELCOME10',
        description: 'Welcome discount for new customers',
        discountType: 'PERCENTAGE',
        discountValue: 10,
        minimumOrderAmount: 500,
        maxUses: 100,
        usedCount: 15,
        isActive: true,
        expiresAt: new Date('2025-12-31'),
      },
      {
        code: 'SUMMER20',
        description: 'Summer sale discount',
        discountType: 'PERCENTAGE',
        discountValue: 20,
        minimumOrderAmount: 1000,
        maxUses: 50,
        usedCount: 8,
        isActive: true,
        expiresAt: new Date('2025-08-31'),
      },
      {
        code: 'FIXED100',
        description: 'Fixed amount discount',
        discountType: 'FIXED',
        discountValue: 100,
        minimumOrderAmount: 800,
        maxUses: 200,
        usedCount: 25,
        isActive: true,
        expiresAt: new Date('2025-12-31'),
      },
    ],
  });

  console.log('✅ Discount codes created');

  // Create sample orders
  const order1 = await prisma.order.create({
    data: {
      userId: customer1.id,
      orderNumber: 'ORD-001',
      status: 'DELIVERED',
      paymentStatus: 'PAID',
      totalAmount: 1999.98,
      shippingAddress: '123 Main St, Maseru, Lesotho',
      phoneNumber: '+266 5000 0001',
      paymentMethod: 'M-Pesa',
      deliveryFee: 50,
      orderItems: {
        create: [
          {
            productId: createdProducts[0].id,
            quantity: 1,
            price: 999.99,
            size: '9',
            color: 'Black',
          },
          {
            productId: createdProducts[1].id,
            quantity: 1,
            price: 999.99,
            size: '10',
            color: 'White',
          },
        ],
      },
    },
  });

  const order2 = await prisma.order.create({
    data: {
      userId: customer2.id,
      orderNumber: 'ORD-002',
      status: 'PROCESSING',
      paymentStatus: 'PAID',
      totalAmount: 1549.98,
      shippingAddress: '456 Oak Ave, Maseru, Lesotho',
      phoneNumber: '+266 5000 0002',
      paymentMethod: 'Bank Transfer',
      deliveryFee: 50,
      orderItems: {
        create: [
          {
            productId: createdProducts[3].id,
            quantity: 1,
            price: 749.99,
            size: '7',
            color: 'Black',
          },
          {
            productId: createdProducts[2].id,
            quantity: 1,
            price: 799.99,
            size: '8',
            color: 'White',
          },
        ],
      },
    },
  });

  console.log('✅ Orders created');

  // Create reviews
  await prisma.review.createMany({
    data: [
      {
        userId: customer1.id,
        productId: createdProducts[0].id,
        rating: 5,
        comment: 'Excellent shoes! Very comfortable and stylish.',
      },
      {
        userId: customer2.id,
        productId: createdProducts[3].id,
        rating: 4,
        comment: 'Great quality and fast delivery. Highly recommended!',
      },
      {
        userId: customer1.id,
        productId: createdProducts[1].id,
        rating: 5,
        comment: 'Perfect fit and amazing comfort. Will buy again!',
      },
    ],
  });

  console.log('✅ Reviews created');

  // Create testimonials
  await prisma.testimonial.createMany({
    data: [
      {
        name: 'John Doe',
        content: 'Amazing service and quality products. Rivv has become my go-to store for footwear!',
        rating: 5,
        isActive: true,
      },
      {
        name: 'Jane Smith',
        content: 'Fast delivery and excellent customer service. The shoes are exactly as described.',
        rating: 5,
        isActive: true,
      },
      {
        name: 'Mike Johnson',
        content: 'Great selection of shoes and competitive prices. Highly recommend Rivv!',
        rating: 4,
        isActive: true,
      },
    ],
  });

  console.log('✅ Testimonials created');

  // Create contact messages
  await prisma.contactMessage.createMany({
    data: [
      {
        name: 'Alice Brown',
        email: '<EMAIL>',
        subject: 'Product Inquiry',
        message: 'Hi, I would like to know more about the Nike Air Max 270. Do you have size 8 in stock?',
        status: 'PENDING',
      },
      {
        name: 'Bob Wilson',
        email: '<EMAIL>',
        subject: 'Order Status',
        message: 'Can you please update me on my order status? Order number: ORD-001',
        status: 'RESPONDED',
        readAt: new Date(),
      },
    ],
  });

  console.log('✅ Contact messages created');

  // Create notices
  await prisma.notice.createMany({
    data: [
      {
        title: 'New Arrivals',
        content: 'Check out our latest collection of premium sneakers now available!',
        type: 'INFO',
        isActive: true,
        expiresAt: new Date('2025-12-31'),
      },
      {
        title: 'Summer Sale',
        content: 'Get up to 20% off on selected items. Limited time offer!',
        type: 'PROMOTION',
        isActive: true,
        expiresAt: new Date('2025-08-31'),
      },
    ],
  });

  console.log('✅ Notices created');

  // Create settings
  await prisma.settings.upsert({
    where: { key: 'site_name' },
    update: {},
    create: {
      key: 'site_name',
      value: 'Rivv E-commerce',
      description: 'The name of the website',
    },
  });

  await prisma.settings.upsert({
    where: { key: 'site_description' },
    update: {},
    create: {
      key: 'site_description',
      value: 'Your premier destination for quality footwear',
      description: 'The description of the website',
    },
  });

  await prisma.settings.upsert({
    where: { key: 'contact_email' },
    update: {},
    create: {
      key: 'contact_email',
      value: '<EMAIL>',
      description: 'Contact email for customer support',
    },
  });

  await prisma.settings.upsert({
    where: { key: 'contact_phone' },
    update: {},
    create: {
      key: 'contact_phone',
      value: '+266 6284 4473',
      description: 'Contact phone number',
    },
  });

  console.log('✅ Settings created');

  console.log('🎉 Database seeding completed successfully!');
  console.log(`
📊 Summary:
- Users: 3 (1 admin, 2 customers)
- Categories: 4
- Products: 10
- Orders: 2
- Reviews: 3
- Testimonials: 3
- Contact Messages: 2
- Notices: 2
- Discount Codes: 3
- Settings: 4

🔐 Login Credentials:
Admin: <EMAIL> / admin123
Customer 1: <EMAIL> / customer123
Customer 2: <EMAIL> / customer123
  `);
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
