import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-client";
import { prisma } from "@/lib/prisma";
import { ApiResponse } from "@/utils/types";

// POST /api/lay-buy/payments - Add payment to lay-buy order
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      layBuyOrderId,
      amount,
      paymentMethod,
      paymentProof,
      notes,
    } = body;

    // Validate required fields
    if (!layBuyOrderId || !amount || amount <= 0) {
      return NextResponse.json(
        { success: false, error: "Missing required fields or invalid amount" },
        { status: 400 }
      );
    }

    // Find the lay-buy order
    const layBuyOrder = await prisma.layBuyOrder.findFirst({
      where: {
        id: layBuyOrderId,
        userId: user.id,
        status: { in: ["ACTIVE", "EXTENDED"] },
      },
    });

    if (!layBuyOrder) {
      return NextResponse.json(
        { success: false, error: "Lay-Buy order not found or not active" },
        { status: 404 }
      );
    }

    // Check if payment amount is valid
    const remainingAmount = layBuyOrder.layBuyAmount - layBuyOrder.amountPaid;
    if (amount > remainingAmount) {
      return NextResponse.json(
        { success: false, error: `Payment amount exceeds remaining balance of M ${remainingAmount.toFixed(2)}` },
        { status: 400 }
      );
    }

    // Create payment and update lay-buy order
    const result = await prisma.$transaction(async (tx) => {
      // Create payment record
      const payment = await tx.layBuyPayment.create({
        data: {
          layBuyOrderId,
          amount,
          status: "PENDING",
          paymentMethod,
          paymentProof,
          notes,
        },
      });

      // Update lay-buy order
      const newAmountPaid = layBuyOrder.amountPaid + amount;
      const newRemainingAmount = layBuyOrder.layBuyAmount - newAmountPaid;
      const isCompleted = newRemainingAmount <= 0.01; // Account for floating point precision

      const updatedOrder = await tx.layBuyOrder.update({
        where: { id: layBuyOrderId },
        data: {
          amountPaid: newAmountPaid,
          remainingAmount: newRemainingAmount,
          status: isCompleted ? "COMPLETED" : layBuyOrder.status,
          completedAt: isCompleted ? new Date() : null,
        },
        include: {
          layBuyItems: {
            include: {
              product: true,
            },
          },
          layBuyPayments: {
            orderBy: { createdAt: "desc" },
          },
        },
      });

      return { payment, layBuyOrder: updatedOrder };
    });

    // TODO: Send payment confirmation email
    // TODO: If completed, trigger order fulfillment process

    const response: ApiResponse = {
      success: true,
      data: result,
      message: result.layBuyOrder.status === "COMPLETED" 
        ? "Payment received! Your Lay-Buy is now complete and will be processed for delivery."
        : "Payment received successfully",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error processing lay-buy payment:", error);
    return NextResponse.json(
      { success: false, error: "Failed to process payment" },
      { status: 500 }
    );
  }
}

// GET /api/lay-buy/payments - Get payments for a lay-buy order
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const layBuyOrderId = searchParams.get("layBuyOrderId");

    if (!layBuyOrderId) {
      return NextResponse.json(
        { success: false, error: "Lay-Buy order ID is required" },
        { status: 400 }
      );
    }

    // Verify user owns the lay-buy order
    const layBuyOrder = await prisma.layBuyOrder.findFirst({
      where: {
        id: layBuyOrderId,
        userId: user.id,
      },
    });

    if (!layBuyOrder) {
      return NextResponse.json(
        { success: false, error: "Lay-Buy order not found" },
        { status: 404 }
      );
    }

    const payments = await prisma.layBuyPayment.findMany({
      where: { layBuyOrderId },
      orderBy: { createdAt: "desc" },
    });

    const response: ApiResponse = {
      success: true,
      data: payments,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching lay-buy payments:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch payments" },
      { status: 500 }
    );
  }
}
