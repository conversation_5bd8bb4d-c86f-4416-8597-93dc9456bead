import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-client";
import { ApiResponse } from "@/utils/types";
import { generateLayBuyAnalytics, generateLayBuyReport, exportLayBuyData } from "@/lib/lay-buy-analytics";

// GET /api/admin/lay-buy/analytics - Get Lay-Buy analytics
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const type = searchParams.get("type") || "analytics"; // analytics, report, export
    const format = searchParams.get("format") || "json"; // json, csv

    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;

    let data;
    let contentType = "application/json";
    let filename = "";

    switch (type) {
      case "report":
        data = await generateLayBuyReport(start, end);
        break;
      
      case "export":
        const exportData = await exportLayBuyData(start, end, format as "csv" | "json");
        
        if (format === "csv") {
          contentType = "text/csv";
          filename = `lay-buy-export-${new Date().toISOString().split('T')[0]}.csv`;
          
          return new NextResponse(exportData, {
            headers: {
              "Content-Type": contentType,
              "Content-Disposition": `attachment; filename="${filename}"`,
            },
          });
        } else {
          data = JSON.parse(exportData);
        }
        break;
      
      default: // analytics
        data = await generateLayBuyAnalytics(start, end);
        break;
    }

    const response: ApiResponse = {
      success: true,
      data,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error generating Lay-Buy analytics:", error);
    return NextResponse.json(
      { success: false, error: "Failed to generate analytics" },
      { status: 500 }
    );
  }
}

// POST /api/admin/lay-buy/analytics - Generate custom report
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { 
      startDate, 
      endDate, 
      reportType = "comprehensive",
      includeCustomerData = false,
      includeFinancialDetails = true,
      format = "json"
    } = body;

    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;

    let data;

    switch (reportType) {
      case "financial":
        const analytics = await generateLayBuyAnalytics(start, end);
        data = {
          period: {
            start: start?.toISOString(),
            end: end?.toISOString(),
          },
          financial: analytics.financialMetrics,
          overview: analytics.overview,
          statusBreakdown: analytics.statusBreakdown,
        };
        break;
      
      case "risk":
        const riskAnalytics = await generateLayBuyAnalytics(start, end);
        data = {
          period: {
            start: start?.toISOString(),
            end: end?.toISOString(),
          },
          riskMetrics: riskAnalytics.riskMetrics,
          alerts: [], // TODO: Generate risk alerts
        };
        break;
      
      case "customer":
        if (!includeCustomerData) {
          return NextResponse.json(
            { success: false, error: "Customer data access not permitted" },
            { status: 403 }
          );
        }
        const customerAnalytics = await generateLayBuyAnalytics(start, end);
        data = {
          period: {
            start: start?.toISOString(),
            end: end?.toISOString(),
          },
          customerInsights: customerAnalytics.customerInsights,
        };
        break;
      
      default: // comprehensive
        data = await generateLayBuyReport(start, end);
        break;
    }

    if (format === "csv" && reportType === "comprehensive") {
      const csvData = await exportLayBuyData(start, end, "csv");
      return new NextResponse(csvData, {
        headers: {
          "Content-Type": "text/csv",
          "Content-Disposition": `attachment; filename="lay-buy-report-${new Date().toISOString().split('T')[0]}.csv"`,
        },
      });
    }

    const response: ApiResponse = {
      success: true,
      data,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error generating custom Lay-Buy report:", error);
    return NextResponse.json(
      { success: false, error: "Failed to generate custom report" },
      { status: 500 }
    );
  }
}
