"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "@/lib/auth-client";
import { AdminRoute } from "@/components/auth/protected-route";
import AdminLayout from "@/components/admin/admin-layout";
import ProductForm from "@/components/admin/products/product-form";
import { getCategories } from "@/actions/categoryActions";
import { User } from "@/utils/types";
import { getUserById } from "@/actions/userActions";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";

export default function AdminAddProductPage() {
  return (
    <AdminRoute>
      <AdminAddProductPageContent />
    </AdminRoute>
  );
}

function AdminAddProductPageContent() {
  const { data: session } = useSession();
  const router = useRouter();
  const [categories, setCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [error, setError] = useState("");
  const [userWithRole, setUserWithRole] = useState<User | null>(null);
  const [isLoadingUser, setIsLoadingUser] = useState(true);

  // At this point, we know the user is authenticated and is an admin due to AdminRoute
  const user = session!.user;

  useEffect(() => {
    const getUserDetails = async () => {
      if (user) {
        try {
          const userResponse = await getUserById(user.id);
          if (userResponse.success && userResponse.data) {
            setUserWithRole(userResponse.data as User);
          }
        } catch (error) {
          console.error("Error fetching user details:", error);
        } finally {
          setIsLoadingUser(false);
        }
      }
    };

    getUserDetails();
  }, [user]);

  useEffect(() => {
    const loadCategories = async () => {
      try {
        const result = await getCategories();
        if (result.success && result.data) {
          setCategories(result.data);
        }
      } catch (error) {
        console.error("Error loading categories:", error);
      } finally {
        setLoadingCategories(false);
      }
    };
    loadCategories();
  }, []);

  if (isLoadingUser || !userWithRole) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <SpinnerCircle4 />
      </div>
    );
  }

  const handleCreate = async (data: any) => {
    setLoading(true);
    setError("");
    try {
      const res = await fetch("/api/admin/products", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      const result = await res.json();
      if (result.success) {
        router.push("/admin/products");
      } else {
        setError(result.error || "Failed to add product");
      }
    } catch (err) {
      setError("Failed to add product");
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdminLayout user={userWithRole}>
      <div className="max-w-4xl mx-auto py-8 px-4">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900">Add New Product</h1>
          <p className="text-gray-600 mt-2">Create a new product for your store</p>
        </div>
        <ProductForm
          categories={categories}
          onSubmit={handleCreate}
          loading={loading}
          error={error}
          mode="create"
        />
      </div>
    </AdminLayout>
  );
} 