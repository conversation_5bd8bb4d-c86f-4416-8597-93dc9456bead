import { NextRequest, NextResponse } from "next/server";
import { ApiResponse } from "@/utils/types";
import { getCurrentUser } from "@/lib/auth-utils";
import prisma from "@/lib/prisma";
import { sendEmail } from "@/lib/email";

// POST /api/admin/contact-messages/[id]/reply - Send reply email (admin only)
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id: paramsId } = await context.params;

    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { replyMessage } = body;

    if (!replyMessage || replyMessage.trim() === "") {
      return NextResponse.json(
        { success: false, error: "Reply message is required" },
        { status: 400 }
      );
    }

    // Get the contact message
    const contactMessage = await prisma.contactMessage.findUnique({
      where: { id: paramsId },
    });

    if (!contactMessage) {
      return NextResponse.json(
        { success: false, error: "Contact message not found" },
        { status: 404 }
      );
    }

    // Send the reply email
    const subjectMap: Record<string, string> = {
      general: "General Inquiry",
      order: "Order Support",
      product: "Product Question",
      shipping: "Shipping & Delivery",
      returns: "Returns & Refunds",
      technical: "Technical Support",
      partnership: "Business Partnership",
      feedback: "Feedback & Suggestions",
    };

    const emailSubject = `Re: ${subjectMap[contactMessage.subject] || contactMessage.subject} - RIVV Support`;
    
    const emailHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>RIVV Support Reply</title>
          <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #1f2937 0%, #374151 100%); color: white; padding: 30px 20px; text-align: center; }
            .brand { font-size: 28px; font-weight: bold; margin-bottom: 8px; letter-spacing: 2px; }
            .tagline { font-size: 14px; opacity: 0.9; font-style: italic; }
            .content { padding: 30px 20px; }
            .reply-section { background-color: #f9fafb; padding: 25px; border-radius: 12px; margin: 25px 0; border-left: 4px solid #3b82f6; }
            .original-message { background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #9ca3af; }
            .footer { background-color: #f9fafb; padding: 25px 20px; text-align: center; color: #6b7280; font-size: 14px; border-top: 1px solid #e5e7eb; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="brand">RIVV</div>
              <div class="tagline">Purposefully Curated. Unapologetically Premium.</div>
            </div>

            <div class="content">
              <h2>Hello ${contactMessage.name},</h2>
              
              <p>Thank you for contacting RIVV Premium Sneakers. We've received your message and here's our response:</p>

              <div class="reply-section">
                <h4 style="margin-top: 0; color: #1e40af;">Our Response:</h4>
                <p style="white-space: pre-wrap; margin: 0;">${replyMessage}</p>
              </div>

              <div class="original-message">
                <h4 style="margin-top: 0; color: #6b7280;">Your Original Message:</h4>
                <p style="white-space: pre-wrap; margin: 0;">${contactMessage.message}</p>
              </div>

              <p>If you have any further questions or need additional assistance, please don't hesitate to reach out to us:</p>
              
              <ul style="margin: 20px 0;">
                <li><strong>WhatsApp:</strong> +266 6284 4473</li>
                <li><strong>Email:</strong> <EMAIL></li>
              </ul>

              <p>Best regards,<br>
              <strong>The RIVV Support Team</strong></p>
            </div>

            <div class="footer">
              <p><strong>RIVV Premium Sneakers</strong><br>
              Purposefully Curated. Unapologetically Premium.</p>
              <p>© 2025 RIVV. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `;

    const emailText = `
      RIVV Premium Sneakers - Support Reply
      
      Hello ${contactMessage.name},
      
      Thank you for contacting RIVV Premium Sneakers. We've received your message and here's our response:
      
      Our Response:
      ${replyMessage}
      
      Your Original Message:
      ${contactMessage.message}
      
      If you have any further questions or need additional assistance, please don't hesitate to reach out to us:
      
      WhatsApp: +266 6284 4473
      Email: <EMAIL>
      
      Best regards,
      The RIVV Support Team
      
      RIVV Premium Sneakers
      Purposefully Curated. Unapologetically Premium.
      © 2025 RIVV. All rights reserved.
    `;

    const emailResult = await sendEmail({
      to: contactMessage.email,
      subject: emailSubject,
      html: emailHtml,
      text: emailText,
    });

    if (!emailResult.success) {
      return NextResponse.json(
        { success: false, error: "Failed to send email" },
        { status: 500 }
      );
    }

    // Update the contact message status to REPLIED
    const updatedMessage = await prisma.contactMessage.update({
      where: { id: paramsId },
      data: {
        status: "REPLIED",
        adminNotes: contactMessage.adminNotes ? 
          `${contactMessage.adminNotes}\n\n--- Reply Sent ---\n${replyMessage}` : 
          `Reply sent: ${replyMessage}`,
      },
    });

    const response: ApiResponse<typeof updatedMessage> = {
      success: true,
      data: updatedMessage,
      message: "Reply email sent successfully",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error sending reply email:", error);
    return NextResponse.json(
      { success: false, error: "Failed to send reply email" },
      { status: 500 }
    );
  }
} 