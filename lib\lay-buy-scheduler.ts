// Lay-Buy Reminder Scheduler
// This file contains utilities for scheduling Lay-Buy reminders

export interface SchedulerConfig {
  cronExpression: string;
  description: string;
  endpoint: string;
}

// Cron job configurations for different platforms
export const LAY_BUY_CRON_JOBS: Record<string, SchedulerConfig> = {
  // Daily check at 9 AM UTC (adjust for your timezone)
  daily: {
    cronExpression: "0 9 * * *",
    description: "Daily Lay-Buy reminder check at 9 AM UTC",
    endpoint: "/api/cron/lay-buy-reminders",
  },
  
  // Twice daily check (9 AM and 6 PM UTC)
  twiceDaily: {
    cronExpression: "0 9,18 * * *",
    description: "Lay-Buy reminder check twice daily (9 AM and 6 PM UTC)",
    endpoint: "/api/cron/lay-buy-reminders",
  },
  
  // Every 6 hours
  sixHourly: {
    cronExpression: "0 */6 * * *",
    description: "Lay-Buy reminder check every 6 hours",
    endpoint: "/api/cron/lay-buy-reminders",
  },
};

// Vercel Cron configuration (vercel.json)
export const VERCEL_CRON_CONFIG = {
  crons: [
    {
      path: "/api/cron/lay-buy-reminders",
      schedule: "0 9 * * *", // Daily at 9 AM UTC
    },
  ],
};

// GitHub Actions workflow configuration
export const GITHUB_ACTIONS_WORKFLOW = `
name: Lay-Buy Reminders
on:
  schedule:
    - cron: '0 9 * * *'  # Daily at 9 AM UTC
  workflow_dispatch:  # Allow manual trigger

jobs:
  send-reminders:
    runs-on: ubuntu-latest
    steps:
      - name: Send Lay-Buy Reminders
        run: |
          curl -X POST \\
            -H "Authorization: Bearer \${{ secrets.CRON_SECRET_TOKEN }}" \\
            -H "Content-Type: application/json" \\
            "\${{ secrets.APP_URL }}/api/cron/lay-buy-reminders"
`;

// Instructions for different deployment platforms
export const DEPLOYMENT_INSTRUCTIONS = {
  vercel: {
    title: "Vercel Cron Jobs",
    steps: [
      "1. Add the following to your vercel.json file:",
      JSON.stringify(VERCEL_CRON_CONFIG, null, 2),
      "2. Set the CRON_SECRET_TOKEN environment variable in Vercel dashboard",
      "3. Deploy your application",
      "4. Cron jobs will run automatically according to the schedule",
    ],
  },
  
  githubActions: {
    title: "GitHub Actions Scheduler",
    steps: [
      "1. Create .github/workflows/lay-buy-reminders.yml with the following content:",
      GITHUB_ACTIONS_WORKFLOW,
      "2. Add these secrets to your GitHub repository:",
      "   - CRON_SECRET_TOKEN: A secure random token",
      "   - APP_URL: Your application URL (e.g., https://yourapp.vercel.app)",
      "3. The workflow will run daily at 9 AM UTC",
      "4. You can also trigger it manually from the Actions tab",
    ],
  },
  
  external: {
    title: "External Cron Service (cron-job.org, EasyCron, etc.)",
    steps: [
      "1. Sign up for an external cron service",
      "2. Create a new cron job with these settings:",
      "   - URL: https://yourapp.com/api/cron/lay-buy-reminders",
      "   - Method: POST",
      "   - Schedule: Daily at 9 AM (or your preferred time)",
      "   - Headers: Authorization: Bearer YOUR_CRON_SECRET_TOKEN",
      "3. Set the CRON_SECRET_TOKEN environment variable in your app",
      "4. Test the cron job to ensure it works correctly",
    ],
  },
  
  manual: {
    title: "Manual Testing",
    steps: [
      "1. Set NODE_ENV=development in your environment",
      "2. Visit /api/cron/lay-buy-reminders in your browser (GET request)",
      "3. Or use curl:",
      "   curl -X POST http://localhost:3000/api/cron/lay-buy-reminders",
      "4. Check the response for results and any errors",
      "5. Monitor your email service for sent reminders",
    ],
  },
};

// Utility function to validate cron expression
export function validateCronExpression(expression: string): boolean {
  // Basic validation for cron expression format
  const parts = expression.trim().split(/\s+/);
  return parts.length === 5; // minute hour day month weekday
}

// Utility function to get next execution time (approximate)
export function getNextExecutionTime(cronExpression: string): Date | null {
  // This is a simplified calculation - in production, use a proper cron parser
  const now = new Date();
  const parts = cronExpression.split(/\s+/);
  
  if (parts.length !== 5) return null;
  
  const [minute, hour] = parts;
  
  // Handle simple daily cron jobs (e.g., "0 9 * * *")
  if (minute === "0" && !hour.includes(",") && !hour.includes("/")) {
    const targetHour = parseInt(hour);
    const next = new Date(now);
    next.setUTCHours(targetHour, 0, 0, 0);
    
    // If the time has passed today, schedule for tomorrow
    if (next <= now) {
      next.setUTCDate(next.getUTCDate() + 1);
    }
    
    return next;
  }
  
  return null; // For complex expressions, return null
}

// Environment variables needed
export const REQUIRED_ENV_VARS = [
  "CRON_SECRET_TOKEN", // Secret token for authenticating cron requests
  "EMAIL_CONFIG_*", // Email service configuration
  "DATABASE_URL", // Database connection
];

// Setup checklist
export const SETUP_CHECKLIST = [
  "✅ Database schema includes Lay-Buy models",
  "✅ Email service is configured and working",
  "✅ Lay-Buy order creation is implemented",
  "✅ Environment variables are set",
  "✅ Cron endpoint is deployed and accessible",
  "✅ Cron job is scheduled on your platform",
  "✅ Test reminders are working correctly",
  "✅ Admin notifications are configured",
  "✅ Error monitoring is in place",
];

// Monitoring and logging utilities
export function logCronJobResult(result: any) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] Lay-Buy Cron Job Result:`, {
    remindersSent: result.remindersSent || 0,
    ordersForfeited: result.ordersForfeited || 0,
    errors: result.errors || [],
    success: result.success || false,
  });
}

// Health check function
export async function healthCheck(): Promise<{
  status: "healthy" | "warning" | "error";
  checks: Record<string, boolean>;
  message: string;
}> {
  const checks = {
    database: false,
    email: false,
    environment: false,
  };

  try {
    // Check database connection
    // This would need to be implemented based on your database setup
    checks.database = true;

    // Check email service
    // This would need to be implemented based on your email service
    checks.email = true;

    // Check environment variables
    checks.environment = REQUIRED_ENV_VARS.every(
      (varName) => process.env[varName] !== undefined
    );

    const allHealthy = Object.values(checks).every(Boolean);
    const someHealthy = Object.values(checks).some(Boolean);

    return {
      status: allHealthy ? "healthy" : someHealthy ? "warning" : "error",
      checks,
      message: allHealthy
        ? "All systems operational"
        : someHealthy
        ? "Some systems have issues"
        : "Critical systems are down",
    };
  } catch (error) {
    return {
      status: "error",
      checks,
      message: `Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}
