"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  CreditCard, 
  Search, 
  Filter, 
  Calendar,
  Package,
  DollarSign,
  Clock,
  AlertTriangle,
  CheckCircle,
  Eye,
  Plus,
  RefreshCw
} from "lucide-react";
import { formatPrice } from "@/lib/product-utils";
import { LayBuyOrder, User } from "@/utils/types";
import { calculateDaysRemaining } from "@/actions/layBuyActions";
import Link from "next/link";

interface LayBuyContentProps {
  user: User;
}

export default function LayBuyContent({ user }: LayBuyContentProps) {
  const [layBuyOrders, setLayBuyOrders] = useState<LayBuyOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchLayBuyOrders();
  }, [statusFilter, currentPage]);

  const fetchLayBuyOrders = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
      });
      
      if (statusFilter !== "all") {
        params.append("status", statusFilter);
      }

      const response = await fetch(`/api/lay-buy/orders?${params}`);
      const result = await response.json();
      
      if (result.success) {
        setLayBuyOrders(result.data.layBuyOrders || []);
        setTotalPages(result.data.pagination?.pages || 1);
      } else {
        setError(result.error || "Failed to fetch Lay-Buy orders");
      }
    } catch (err) {
      setError("Failed to load Lay-Buy orders");
    } finally {
      setLoading(false);
    }
  };

  const filteredOrders = layBuyOrders.filter(order =>
    order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.layBuyItems?.some(item => 
      item.product.name.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ACTIVE": return "bg-blue-100 text-blue-800";
      case "COMPLETED": return "bg-green-100 text-green-800";
      case "CANCELLED": return "bg-gray-100 text-gray-800";
      case "FORFEITED": return "bg-red-100 text-red-800";
      case "EXTENDED": return "bg-yellow-100 text-yellow-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "ACTIVE": return <Clock className="h-4 w-4" />;
      case "COMPLETED": return <CheckCircle className="h-4 w-4" />;
      case "CANCELLED": return <RefreshCw className="h-4 w-4" />;
      case "FORFEITED": return <AlertTriangle className="h-4 w-4" />;
      case "EXTENDED": return <Calendar className="h-4 w-4" />;
      default: return <Package className="h-4 w-4" />;
    }
  };

  const activeOrders = layBuyOrders.filter(order => order.status === "ACTIVE" || order.status === "EXTENDED");
  const completedOrders = layBuyOrders.filter(order => order.status === "COMPLETED");
  const totalPaid = layBuyOrders.reduce((sum, order) => sum + order.amountPaid, 0);
  const totalOutstanding = activeOrders.reduce((sum, order) => sum + order.remainingAmount, 0);

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">My Lay-Buy Orders</h1>
        <p className="text-gray-600">Manage your Lay-Buy orders and track payment progress</p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Orders</p>
                <p className="text-2xl font-bold text-blue-600">{activeOrders.length}</p>
              </div>
              <Clock className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-green-600">{completedOrders.length}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Paid</p>
                <p className="text-2xl font-bold text-green-600">{formatPrice(totalPaid)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Outstanding</p>
                <p className="text-2xl font-bold text-orange-600">{formatPrice(totalOutstanding)}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search by order number or product name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                  <SelectItem value="FORFEITED">Forfeited</SelectItem>
                  <SelectItem value="EXTENDED">Extended</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" onClick={fetchLayBuyOrders}>
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Orders List */}
      <Card>
        <CardHeader>
          <CardTitle>Lay-Buy Orders</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : error ? (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          ) : filteredOrders.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Lay-Buy Orders Found</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || statusFilter !== "all" 
                  ? "Try adjusting your search or filter criteria."
                  : "Start a Lay-Buy order to pay for items over time with just 60% upfront."
                }
              </p>
              {!searchTerm && statusFilter === "all" && (
                <Link href="/products">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Start Shopping
                  </Button>
                </Link>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredOrders.map((order) => {
                const timing = calculateDaysRemaining(new Date(order.dueDate), new Date(order.gracePeriodEnd));
                const paymentProgress = (order.amountPaid / order.layBuyAmount) * 100;

                return (
                  <div key={order.id} className="border rounded-lg p-4 space-y-4">
                    {/* Order Header */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <h3 className="font-semibold">Order #{order.orderNumber}</h3>
                        <Badge className={getStatusColor(order.status)}>
                          {getStatusIcon(order.status)}
                          <span className="ml-1">{order.status}</span>
                        </Badge>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-gray-600">
                          Created: {new Date(order.createdAt).toLocaleDateString()}
                        </div>
                        {order.status === "ACTIVE" && (
                          <div className="text-sm font-medium">
                            {timing.isOverdue ? (
                              <span className="text-red-600">
                                {timing.isInGracePeriod 
                                  ? `Grace: ${Math.abs(timing.daysUntilForfeiture)} days left`
                                  : `Overdue by ${Math.abs(timing.daysUntilDue)} days`
                                }
                              </span>
                            ) : (
                              <span className="text-blue-600">
                                {timing.daysUntilDue} days remaining
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Payment Progress */}
                    {order.status === "ACTIVE" && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Payment Progress</span>
                          <span>{formatPrice(order.amountPaid)} / {formatPrice(order.layBuyAmount)}</span>
                        </div>
                        <Progress value={paymentProgress} className="h-2" />
                        <div className="flex justify-between text-xs text-gray-600">
                          <span>{paymentProgress.toFixed(1)}% completed</span>
                          <span>{formatPrice(order.remainingAmount)} remaining</span>
                        </div>
                      </div>
                    )}

                    {/* Order Details */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Items:</span>
                        <span className="ml-2 font-medium">{order.layBuyItems?.length || 0}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Original Total:</span>
                        <span className="ml-2 font-medium">{formatPrice(order.originalAmount)}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Lay-Buy Amount:</span>
                        <span className="ml-2 font-medium">{formatPrice(order.layBuyAmount)}</span>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2 pt-2">
                      <Link href={`/lay-buy/${order.id}`}>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                      </Link>
                      {order.remainingAmount > 0 && order.status === "ACTIVE" && (
                        <Link href={`/lay-buy/${order.id}/payment`}>
                          <Button size="sm">
                            <DollarSign className="h-4 w-4 mr-2" />
                            Make Payment
                          </Button>
                        </Link>
                      )}
                    </div>
                  </div>
                );
              })}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center gap-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="flex items-center px-4">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
