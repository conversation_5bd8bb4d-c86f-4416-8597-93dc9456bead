import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth-client";
import { prisma } from "@/lib/prisma";
import { ApiResponse } from "@/utils/types";

// GET /api/admin/lay-buy/orders - Get all lay-buy orders (admin only)
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get("status");
    const search = searchParams.get("search");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const skip = (page - 1) * limit;

    const where: any = {};
    
    if (status) {
      where.status = status;
    }

    if (search) {
      where.OR = [
        { orderNumber: { contains: search, mode: "insensitive" } },
        { user: { name: { contains: search, mode: "insensitive" } } },
        { user: { email: { contains: search, mode: "insensitive" } } },
        { phoneNumber: { contains: search, mode: "insensitive" } },
      ];
    }

    const [layBuyOrders, total] = await Promise.all([
      prisma.layBuyOrder.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          layBuyItems: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  images: true,
                },
              },
            },
          },
          layBuyPayments: {
            orderBy: { createdAt: "desc" },
            take: 1, // Latest payment only for list view
          },
          _count: {
            select: {
              layBuyPayments: true,
              layBuyReminders: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.layBuyOrder.count({ where }),
    ]);

    // Calculate summary statistics
    const summary = await prisma.layBuyOrder.aggregate({
      _count: { id: true },
      _sum: {
        originalAmount: true,
        layBuyAmount: true,
        amountPaid: true,
        remainingAmount: true,
      },
      where: { status: { in: ["ACTIVE", "EXTENDED"] } },
    });

    const statusCounts = await prisma.layBuyOrder.groupBy({
      by: ["status"],
      _count: { id: true },
    });

    const response: ApiResponse = {
      success: true,
      data: {
        layBuyOrders,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
        summary: {
          totalActive: summary._count.id || 0,
          totalValue: summary._sum.originalAmount || 0,
          totalPaid: summary._sum.amountPaid || 0,
          totalOutstanding: summary._sum.remainingAmount || 0,
          statusBreakdown: statusCounts.reduce((acc, item) => {
            acc[item.status] = item._count.id;
            return acc;
          }, {} as Record<string, number>),
        },
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching admin lay-buy orders:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch lay-buy orders" },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/lay-buy/orders - Bulk update lay-buy orders
export async function PATCH(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { orderIds, action, data } = body;

    if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
      return NextResponse.json(
        { success: false, error: "Order IDs are required" },
        { status: 400 }
      );
    }

    let updateData: any = {};
    let additionalActions: any[] = [];

    switch (action) {
      case "extend":
        const extensionDays = data?.days || 7;
        updateData = {
          status: "EXTENDED",
          dueDate: {
            increment: extensionDays * 24 * 60 * 60 * 1000, // Convert days to milliseconds
          },
          adminNotes: `Extended by ${extensionDays} days by admin`,
        };
        break;

      case "forfeit":
        updateData = {
          status: "FORFEITED",
          forfeitedAt: new Date(),
          adminNotes: data?.reason || "Forfeited by admin",
        };
        // TODO: Add logic to restore stock
        break;

      case "complete":
        updateData = {
          status: "COMPLETED",
          completedAt: new Date(),
          remainingAmount: 0,
          adminNotes: "Manually completed by admin",
        };
        break;

      case "cancel":
        updateData = {
          status: "CANCELLED",
          cancelledAt: new Date(),
          refundAmount: data?.refundAmount || 0,
          adminNotes: data?.reason || "Cancelled by admin",
        };
        // TODO: Add logic to restore stock
        break;

      default:
        return NextResponse.json(
          { success: false, error: "Invalid action" },
          { status: 400 }
        );
    }

    const updatedOrders = await prisma.layBuyOrder.updateMany({
      where: {
        id: { in: orderIds },
      },
      data: updateData,
    });

    // TODO: Send notification emails to affected customers
    // TODO: Process any additional actions (stock restoration, etc.)

    const response: ApiResponse = {
      success: true,
      data: { updatedCount: updatedOrders.count },
      message: `Successfully ${action}ed ${updatedOrders.count} lay-buy order(s)`,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error updating lay-buy orders:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update lay-buy orders" },
      { status: 500 }
    );
  }
}
