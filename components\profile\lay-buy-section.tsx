"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Clock, 
  CreditCard, 
  AlertTriangle, 
  CheckCircle, 
  Calendar,
  DollarSign,
  Package,
  Eye,
  Plus
} from "lucide-react";
import { formatPrice } from "@/lib/product-utils";
import { LayBuyOrder } from "@/utils/types";
import { calculateDaysRemaining } from "@/actions/layBuyActions";
import Link from "next/link";

interface LayBuySectionProps {
  userId: string;
}

export default function LayBuySection({ userId }: LayBuySectionProps) {
  const [layBuyOrders, setLayBuyOrders] = useState<LayBuyOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchLayBuyOrders();
  }, [userId]);

  const fetchLayBuyOrders = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/lay-buy/orders?status=ACTIVE&limit=5`);
      const result = await response.json();
      
      if (result.success) {
        setLayBuyOrders(result.data.layBuyOrders || []);
      } else {
        setError(result.error || "Failed to fetch Lay-Buy orders");
      }
    } catch (err) {
      setError("Failed to load Lay-Buy orders");
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ACTIVE": return "bg-blue-100 text-blue-800";
      case "COMPLETED": return "bg-green-100 text-green-800";
      case "CANCELLED": return "bg-gray-100 text-gray-800";
      case "FORFEITED": return "bg-red-100 text-red-800";
      case "EXTENDED": return "bg-yellow-100 text-yellow-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getUrgencyLevel = (daysRemaining: number) => {
    if (daysRemaining <= 0) return "overdue";
    if (daysRemaining <= 3) return "urgent";
    if (daysRemaining <= 7) return "warning";
    return "normal";
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "overdue": return "text-red-600";
      case "urgent": return "text-red-500";
      case "warning": return "text-yellow-600";
      default: return "text-gray-600";
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            My Lay-Buy Orders
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            My Lay-Buy Orders
          </CardTitle>
          <Link href="/lay-buy">
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-2" />
              View All
            </Button>
          </Link>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {layBuyOrders.length === 0 ? (
          <div className="text-center py-8">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Lay-Buy Orders</h3>
            <p className="text-gray-600 mb-4">
              Start a Lay-Buy order to pay for items over time with just 60% upfront.
            </p>
            <Link href="/products">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Start Shopping
              </Button>
            </Link>
          </div>
        ) : (
          <div className="space-y-4">
            {layBuyOrders.map((order) => {
              const timing = calculateDaysRemaining(new Date(order.dueDate), new Date(order.gracePeriodEnd));
              const urgency = getUrgencyLevel(timing.daysUntilDue);
              const paymentProgress = (order.amountPaid / order.layBuyAmount) * 100;

              return (
                <div key={order.id} className="border rounded-lg p-4 space-y-3">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <h4 className="font-medium">Order #{order.orderNumber}</h4>
                      <Badge className={getStatusColor(order.status)}>
                        {order.status}
                      </Badge>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-600">
                        {timing.isOverdue ? (
                          timing.isInGracePeriod ? (
                            <span className="text-red-600 font-medium">
                              Grace Period: {Math.abs(timing.daysUntilForfeiture)} days left
                            </span>
                          ) : timing.isForfeited ? (
                            <span className="text-red-600 font-medium">Forfeited</span>
                          ) : (
                            <span className="text-red-600 font-medium">
                              Overdue by {Math.abs(timing.daysUntilDue)} days
                            </span>
                          )
                        ) : (
                          <span className={getUrgencyColor(urgency)}>
                            {timing.daysUntilDue} days remaining
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Payment Progress */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Payment Progress</span>
                      <span>{formatPrice(order.amountPaid)} / {formatPrice(order.layBuyAmount)}</span>
                    </div>
                    <Progress value={paymentProgress} className="h-2" />
                    <div className="flex justify-between text-xs text-gray-600">
                      <span>{paymentProgress.toFixed(1)}% paid</span>
                      <span>{formatPrice(order.remainingAmount)} remaining</span>
                    </div>
                  </div>

                  {/* Items Summary */}
                  <div className="text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4" />
                      <span>{order.layBuyItems?.length || 0} item(s)</span>
                      <span>•</span>
                      <span>Original: {formatPrice(order.originalAmount)}</span>
                    </div>
                  </div>

                  {/* Due Date Warning */}
                  {(urgency === "urgent" || urgency === "overdue") && (
                    <Alert className="border-red-200 bg-red-50">
                      <AlertTriangle className="h-4 w-4 text-red-600" />
                      <AlertDescription className="text-red-700">
                        {timing.isOverdue ? (
                          timing.isInGracePeriod ? (
                            <>
                              <strong>Grace Period Active:</strong> Complete payment within {Math.abs(timing.daysUntilForfeiture)} days to avoid forfeiture.
                            </>
                          ) : (
                            <>
                              <strong>Payment Overdue:</strong> Your payment is {Math.abs(timing.daysUntilDue)} days overdue.
                            </>
                          )
                        ) : (
                          <>
                            <strong>Payment Due Soon:</strong> Complete payment within {timing.daysUntilDue} days.
                          </>
                        )}
                      </AlertDescription>
                    </Alert>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-2 pt-2">
                    <Link href={`/lay-buy/${order.id}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                    </Link>
                    {order.remainingAmount > 0 && order.status === "ACTIVE" && (
                      <Link href={`/lay-buy/${order.id}/payment`}>
                        <Button size="sm">
                          <DollarSign className="h-4 w-4 mr-2" />
                          Make Payment
                        </Button>
                      </Link>
                    )}
                  </div>
                </div>
              );
            })}

            {/* Summary Stats */}
            <Separator />
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-2">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {layBuyOrders.length}
                </div>
                <div className="text-xs text-gray-600">Active Orders</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {formatPrice(layBuyOrders.reduce((sum, order) => sum + order.amountPaid, 0))}
                </div>
                <div className="text-xs text-gray-600">Total Paid</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {formatPrice(layBuyOrders.reduce((sum, order) => sum + order.remainingAmount, 0))}
                </div>
                <div className="text-xs text-gray-600">Outstanding</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {layBuyOrders.filter(order => {
                    const timing = calculateDaysRemaining(new Date(order.dueDate), new Date(order.gracePeriodEnd));
                    return timing.daysUntilDue <= 7;
                  }).length}
                </div>
                <div className="text-xs text-gray-600">Due Soon</div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
