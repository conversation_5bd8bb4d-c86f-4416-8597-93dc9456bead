import { sendEmail, emailTemplates, EMAIL_CONFIG } from "./email";
import { Order, ContactMessage, User, LayBuyOrder } from "@/utils/types";
import { formatPrice } from "./product-utils";
import { layBuyEmailTemplates, LayBuyEmailData } from "./lay-buy-email-templates";

// Type for order status update email - more flexible than full Order type
type OrderForStatusUpdate = {
  id: string;
  orderNumber: string;
  totalAmount: number;
  user?: User | null;
  orderItems: Array<{
    quantity: number;
    price: number;
    size: string | null;
    product: {
      name: string;
    };
  }>;
};

// Send order confirmation email
export async function sendOrderConfirmationEmail(order: Order) {
  try {
    const orderItems = order.orderItems.map(item => ({
      name: item.product.name,
      quantity: item.quantity,
      price: formatPrice(item.price),
      size: item.size || undefined,
    }));

    const template = emailTemplates.orderConfirmation({
      customerName: order.user?.name || "Customer",
      orderNumber: order.orderNumber,
      orderTotal: formatPrice(order.totalAmount),
      orderItems,
      shippingAddress: order.shippingAddress,
      phoneNumber: order.phoneNumber,
      orderUrl: `${EMAIL_CONFIG.baseUrl}/orders/${order.id}`,
    });

    const result = await sendEmail({
      to: order.user?.email || "",
      subject: template.subject,
      html: template.html,
      text: template.text,
    });

    return result;
  } catch (error) {
    console.error("Error sending order confirmation email:", error);
    return { success: false, error: "Failed to send order confirmation email" };
  }
}

// Send admin order notification email
export async function sendAdminOrderNotification(order: Order, paymentMethod?: string) {
  try {
    const adminEmail = "<EMAIL>";

    const orderItems = order.orderItems.map(item => ({
      name: item.product.name,
      quantity: item.quantity,
      price: formatPrice(item.price),
      size: item.size || undefined,
    }));

    const template = emailTemplates.adminOrderNotification({
      orderNumber: order.orderNumber,
      customerName: order.user?.name || "Customer",
      customerEmail: order.user?.email || "",
      customerPhone: order.phoneNumber,
      orderTotal: formatPrice(order.totalAmount),
      orderItems,
      shippingAddress: order.shippingAddress,
      paymentMethod,
      notes: order.notes || undefined,
      orderUrl: `${EMAIL_CONFIG.baseUrl}/admin/orders/${order.id}`,
      createdAt: new Date(order.createdAt).toLocaleString('en-LS', {
        timeZone: 'Africa/Maseru',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      }),
    });

    const result = await sendEmail({
      to: adminEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });

    return result;
  } catch (error) {
    console.error("Error sending admin order notification:", error);
    return { success: false, error: "Failed to send admin order notification" };
  }
}

// Send contact form confirmation email
export async function sendContactConfirmationEmail(contactMessage: ContactMessage) {
  try {
    const subjectMap: Record<string, string> = {
      general: "General Inquiry",
      order: "Order Support",
      product: "Product Question",
      shipping: "Shipping & Delivery",
      returns: "Returns & Refunds",
      technical: "Technical Support",
      partnership: "Business Partnership",
      feedback: "Feedback & Suggestions",
    };

    const template = emailTemplates.contactConfirmation({
      customerName: contactMessage.name,
      subject: subjectMap[contactMessage.subject] || contactMessage.subject,
    });

    const result = await sendEmail({
      to: contactMessage.email,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });

    return result;
  } catch (error) {
    console.error("Error sending contact confirmation email:", error);
    return { success: false, error: "Failed to send contact confirmation email" };
  }
}



// Send password reset email
export async function sendPasswordResetEmail(email: string, name: string, resetToken: string) {
  try {
    const resetUrl = `${EMAIL_CONFIG.baseUrl}/reset-password?token=${resetToken}`;

    const template = emailTemplates.passwordReset({
      customerName: name,
      resetUrl,
    });

    const result = await sendEmail({
      to: email,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });

    return result;
  } catch (error) {
    console.error("Error sending password reset email:", error);
    return { success: false, error: "Failed to send password reset email" };
  }
}

// Send order status update email
export async function sendOrderStatusUpdateEmail(order: OrderForStatusUpdate, newStatus: string, trackingInfo?: string, cancellationReason?: string) {
  try {
    const statusMessages: Record<string, string> = {
      CONFIRMED: "Great news! Your payment has been verified and your order is confirmed. We're now preparing your items for shipment.",
      PROCESSING: "Your order is now being processed. Our team is carefully preparing your items for shipment.",
      SHIPPED: "Exciting news! Your order has been shipped and is on its way to you. You should receive it within 3-5 business days.",
      DELIVERED: "Your order has been successfully delivered! We hope you love your new items. Thank you for shopping with RIVV!",
      CANCELLED: "We're sorry to inform you that your order has been cancelled. If you have any questions, please don't hesitate to contact us.",
    };

    const statusMessage = statusMessages[newStatus];
    if (!statusMessage) {
      return { success: false, error: "Invalid status for email notification" };
    }

    const orderItems = order.orderItems.map(item => ({
      name: item.product.name,
      quantity: item.quantity,
      price: formatPrice(item.price),
      size: item.size || undefined,
    }));

    // Calculate estimated delivery for shipped orders (3-5 business days)
    let estimatedDelivery: string | undefined;
    if (newStatus === 'SHIPPED') {
      const deliveryDate = new Date();
      deliveryDate.setDate(deliveryDate.getDate() + 5); // 5 business days
      estimatedDelivery = deliveryDate.toLocaleDateString('en-LS', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    }

    const template = emailTemplates.orderStatusUpdate({
      customerName: order.user?.name || "Customer",
      orderNumber: order.orderNumber,
      status: newStatus,
      statusMessage,
      orderTotal: formatPrice(order.totalAmount),
      orderItems,
      orderUrl: `${EMAIL_CONFIG.baseUrl}/orders/${order.id}`,
      trackingInfo,
      estimatedDelivery,
      cancellationReason,
    });

    const result = await sendEmail({
      to: order.user?.email || "",
      subject: template.subject,
      html: template.html,
      text: template.text,
    });

    return result;
  } catch (error) {
    console.error("Error sending order status update email:", error);
    return { success: false, error: "Failed to send order status update email" };
  }
}

// Send welcome email for new users
export async function sendWelcomeEmail(email: string, name: string) {
  try {
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Welcome to Rivv</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px; margin-bottom: 20px; }
            .button { display: inline-block; background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Welcome to Rivv!</h1>
            </div>
            
            <p>Hi ${name},</p>
            
            <p>Welcome to Rivv E-commerce! We're excited to have you as part of our community.</p>
            
            <p>Here's what you can do with your new account:</p>
            <ul>
              <li>Browse our extensive product catalog</li>
              <li>Save items to your wishlist</li>
              <li>Track your orders in real-time</li>
              <li>Enjoy exclusive member discounts</li>
              <li>Get priority customer support</li>
            </ul>
            
            <a href="${EMAIL_CONFIG.baseUrl}/products" class="button">Start Shopping</a>
            
            <p>If you have any questions, our support team is here to <NAME_EMAIL></p>
            
            <div class="footer">
              <p>Thank you for choosing Rivv!</p>
              <p>© 2024 Rivv E-commerce. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `;

    const text = `
      Welcome to Rivv!
      
      Hi ${name},
      
      Welcome to Rivv E-commerce! We're excited to have you as part of our community.
      
      Here's what you can do with your new account:
      - Browse our extensive product catalog
      - Save items to your wishlist
      - Track your orders in real-time
      - Enjoy exclusive member discounts
      - Get priority customer support
      
      Start shopping: ${EMAIL_CONFIG.baseUrl}/products
      
      If you have any questions, our support team is here to <NAME_EMAIL>
      
      Thank you for choosing Rivv!
    `;

    const result = await sendEmail({
      to: email,
      subject: "Welcome to Rivv - Your Account is Ready!",
      html,
      text,
    });

    return result;
  } catch (error) {
    console.error("Error sending welcome email:", error);
    return { success: false, error: "Failed to send welcome email" };
  }
}

// Lay-Buy Email Functions

// Send Lay-Buy confirmation email
export async function sendLayBuyConfirmationEmail(layBuyOrder: LayBuyOrder) {
  try {
    const emailData: LayBuyEmailData = {
      customerName: layBuyOrder.user?.name || "Customer",
      orderNumber: layBuyOrder.orderNumber,
      layBuyAmount: layBuyOrder.layBuyAmount,
      amountPaid: layBuyOrder.amountPaid,
      remainingAmount: layBuyOrder.remainingAmount,
      dueDate: new Date(layBuyOrder.dueDate).toLocaleDateString(),
      daysRemaining: Math.ceil((new Date(layBuyOrder.dueDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24)),
      weekNumber: 1,
      orderItems: layBuyOrder.layBuyItems?.map(item => ({
        name: item.product.name,
        quantity: item.quantity,
        price: formatPrice(item.layBuyPrice),
        size: item.size || undefined,
      })) || [],
      orderUrl: `${EMAIL_CONFIG.baseUrl}/lay-buy/${layBuyOrder.id}`,
      paymentUrl: `${EMAIL_CONFIG.baseUrl}/lay-buy/${layBuyOrder.id}/payment`,
    };

    const template = layBuyEmailTemplates.weeklyReminder(emailData);

    const result = await sendEmail({
      to: layBuyOrder.user?.email || "",
      subject: `Lay-Buy Order Confirmed - #${layBuyOrder.orderNumber}`,
      html: template.html,
      text: template.text,
    });

    console.log("Lay-Buy confirmation email sent successfully");
    return result;
  } catch (error) {
    console.error("Error sending Lay-Buy confirmation email:", error);
    throw error;
  }
}

// Send weekly Lay-Buy reminder
export async function sendLayBuyWeeklyReminder(layBuyOrder: LayBuyOrder, weekNumber: number) {
  try {
    const daysRemaining = Math.ceil((new Date(layBuyOrder.dueDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24));

    const emailData: LayBuyEmailData = {
      customerName: layBuyOrder.user?.name || "Customer",
      orderNumber: layBuyOrder.orderNumber,
      layBuyAmount: layBuyOrder.layBuyAmount,
      amountPaid: layBuyOrder.amountPaid,
      remainingAmount: layBuyOrder.remainingAmount,
      dueDate: new Date(layBuyOrder.dueDate).toLocaleDateString(),
      daysRemaining,
      weekNumber,
      orderItems: layBuyOrder.layBuyItems?.map(item => ({
        name: item.product.name,
        quantity: item.quantity,
        price: formatPrice(item.layBuyPrice),
        size: item.size || undefined,
      })) || [],
      orderUrl: `${EMAIL_CONFIG.baseUrl}/lay-buy/${layBuyOrder.id}`,
      paymentUrl: `${EMAIL_CONFIG.baseUrl}/lay-buy/${layBuyOrder.id}/payment`,
    };

    let template;
    if (weekNumber >= 5 && daysRemaining <= 0) {
      // Grace period
      template = layBuyEmailTemplates.gracePeriodReminder(emailData);
    } else if (weekNumber === 6 || daysRemaining <= 0) {
      // Final warning
      template = layBuyEmailTemplates.finalWarning(emailData);
    } else if (weekNumber === 5 || daysRemaining <= 7) {
      // Urgent reminder
      template = layBuyEmailTemplates.urgentReminder(emailData);
    } else {
      // Regular weekly reminder
      template = layBuyEmailTemplates.weeklyReminder(emailData);
    }

    const result = await sendEmail({
      to: layBuyOrder.user?.email || "",
      subject: template.subject,
      html: template.html,
      text: template.text,
    });

    console.log(`Lay-Buy week ${weekNumber} reminder sent successfully`);
    return result;
  } catch (error) {
    console.error("Error sending Lay-Buy weekly reminder:", error);
    throw error;
  }
}

// Send Lay-Buy payment confirmation
export async function sendLayBuyPaymentConfirmation(
  layBuyOrder: LayBuyOrder,
  paymentAmount: number,
  isCompleted: boolean = false
) {
  try {
    const daysRemaining = Math.ceil((new Date(layBuyOrder.dueDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24));

    const emailData: LayBuyEmailData & { paymentAmount: number; isCompleted: boolean } = {
      customerName: layBuyOrder.user?.name || "Customer",
      orderNumber: layBuyOrder.orderNumber,
      layBuyAmount: layBuyOrder.layBuyAmount,
      amountPaid: layBuyOrder.amountPaid,
      remainingAmount: layBuyOrder.remainingAmount,
      dueDate: new Date(layBuyOrder.dueDate).toLocaleDateString(),
      daysRemaining,
      weekNumber: 0, // Not applicable for payment confirmation
      orderItems: layBuyOrder.layBuyItems?.map(item => ({
        name: item.product.name,
        quantity: item.quantity,
        price: formatPrice(item.layBuyPrice),
        size: item.size || undefined,
      })) || [],
      orderUrl: `${EMAIL_CONFIG.baseUrl}/lay-buy/${layBuyOrder.id}`,
      paymentUrl: `${EMAIL_CONFIG.baseUrl}/lay-buy/${layBuyOrder.id}/payment`,
      paymentAmount,
      isCompleted,
    };

    const template = layBuyEmailTemplates.paymentConfirmation(emailData);

    const result = await sendEmail({
      to: layBuyOrder.user?.email || "",
      subject: template.subject,
      html: template.html,
      text: template.text,
    });

    console.log("Lay-Buy payment confirmation sent successfully");
    return result;
  } catch (error) {
    console.error("Error sending Lay-Buy payment confirmation:", error);
    throw error;
  }
}
