"use client";

import { useState } from "react";
import { CheckoutData } from "./checkout-content";
import { useCart } from "@/contexts/cart-context";
import { Button } from "@/components/ui/button";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { calculateDeliveryFee } from "@/lib/product-utils";
import LayBuyOption from "../lay-buy/lay-buy-option";

interface CheckoutStepPaymentOptionProps {
  data: CheckoutData;
  onUpdate: (data: Partial<CheckoutData>) => void;
  onNext: () => void;
  onPrev: () => void;
}

export default function CheckoutStepPaymentOption({
  data,
  onUpdate,
  onNext,
  onPrev,
}: CheckoutStepPaymentOptionProps) {
  const { state: cartState } = useCart();

  const subtotal = cartState.totalPrice;
  const discountAmount = data.discountAmount || 0;
  const deliveryInfo = calculateDeliveryFee(data.shipping.district, subtotal - discountAmount);
  const deliveryCost = deliveryInfo.fee;
  const totalAmount = subtotal - discountAmount + deliveryCost;

  const handleLayBuySelect = (isLayBuy: boolean, layBuyAmount?: number) => {
    onUpdate({
      paymentType: isLayBuy ? "laybuy" : "full",
      layBuyAmount: layBuyAmount || 0,
      termsAccepted: isLayBuy,
    });
  };

  const handleNext = () => {
    onNext();
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold mb-2">Choose Your Payment Option</h2>
        <p className="text-gray-600">
          Select how you'd like to pay for your order
        </p>
      </div>

      <LayBuyOption
        totalAmount={totalAmount}
        onLayBuySelect={handleLayBuySelect}
        selectedPaymentType={data.paymentType}
      />

      {/* Navigation Buttons */}
      <div className="flex justify-between pt-6">
        <Button variant="outline" onClick={onPrev}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Shipping
        </Button>
        
        <Button onClick={handleNext}>
          Continue to Review
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
